from fastapi import HTTPException
from postgrest import APIError, APIResponse

from context import ApplicationContext
from.user_models import UserCreate, UserResponse


class UserController:
    """
    Controller class for user management operations.
    
    This class contains the business logic for user operations,
    keeping it separate from the API routing layer.
    """
    
    def __init__(self, context: ApplicationContext):
        """
        Initialize the user controller.
        
        Args:
            context: The application context containing shared resources.
        """
        self.context = context
    
    async def create_user(self, user_data: UserCreate) -> UserResponse:
        """
        Create a new user and assign them a role.
        
        Args:
            user_data: The user creation data including email, password, and role.
            
        Returns:
            UserResponse: The created user information.
            
        Raises:
            HTTPException: If user creation or role assignment fails.
        """
        try:
            # 1. Create the user in Supabase Auth
            # We auto-confirm the email as it's created by an admin.
            new_user_response = self.context.supabase.auth.admin.create_user({
                "email": user_data.email,
                "password": user_data.password,
                "email_confirm": True,
            })
            
            new_user = new_user_response.user
            if not new_user:
                raise HTTPException(
                    status_code=400, 
                    detail="User creation failed in Supabase Auth."
                )

            # 2. Assign the role in the 'user_roles' table.
            # The 'role' column now directly accepts the role name as an ENUM value.
            role_assignment_response: APIResponse = self.context.supabase.table("user_roles").insert({
                "user_id": new_user.id,
                "role": user_data.role
            }).execute()

            return UserResponse(
                id=new_user.id,
                email=new_user.email,
                created_at=new_user.created_at.isoformat()
            )

        except APIError as e:
            # Catch specific Supabase/PostgREST API errors
            raise HTTPException(status_code=400, detail=e.message)
        except HTTPException:
            # Re-raise HTTP exceptions that we've already crafted
            raise
        except Exception as e:
            # Convert other unexpected exceptions to a generic 500 error
            raise HTTPException(status_code=500, detail=str(e))
    
    async def update_user_role(self, user_id: str, role_name: str) -> None:
        """
        Updates the role for an existing user.
        
        This assumes a user can only have one role at a time, as per the JWT hook design.
        
        Args:
            user_id: The UUID of the user whose role is to be updated.
            role_name: The new role to assign (e.g., 'operator', 'manager').
            
        Raises:
            HTTPException: If the user is not found or the role update fails.
        """
        try:
            # Update the user's role in the user_roles table.
            response: APIResponse = self.context.supabase.table("user_roles").update({
                "role": role_name
            }).eq("user_id", user_id).execute()
            
            # The update operation returns the updated rows in the data attribute.
            # If no rows were updated, it means the user_id was not found in the table.
            if not response.data:
                raise HTTPException(
                    status_code=404, 
                    detail=f"User with ID '{user_id}' not found or no role to update."
                )

        except APIError as e:
            raise HTTPException(status_code=400, detail=e.message)
        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))