from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):

    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = False
    supabase_project_url: str = ""
    supabase_service_key: str = ""

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )


settings = Settings()
