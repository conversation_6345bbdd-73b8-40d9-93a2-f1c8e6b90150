from contextlib import asynccontextmanager

from fastapi import FastAPI

from context import get_app_context
from api.users.user_routes import router as user_router


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan context manager.

    Handles initialization and cleanup of application resources.
    """
    # Startup
    context = await get_app_context()
    await context.initialize()

    yield

    # Shutdown
    await context.cleanup()


app = FastAPI(
    title="Cortexa User Management Service",
    description="API for managing user roles and creation.",
    version="1.0.0",
    lifespan=lifespan,
)

# Include routers
app.include_router(user_router)
