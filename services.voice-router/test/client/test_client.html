<!DOCTYPE html>
<html>
<head>
    <title>Voice Router Test Client</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { font-family: sans-serif; margin: 2em; background: #f4f4f9; color: #333; }
        h1, h2 { color: #444; }
       .container { max-width: 800px; margin: auto; background: white; padding: 2em; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        input, button { font-size: 1em; padding: 0.5em 1em; margin: 0.5em 0.5em 0.5em 0; border-radius: 4px; border: 1px solid #ccc; }
        button { cursor: pointer; background: #007bff; color: white; border-color: #007bff; }
        button.mute { background: #dc3545; border-color: #dc3545; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        #status { background: #eee; border-left: 4px solid #007bff; padding: 1em; margin-top: 1em; white-space: pre-wrap; word-wrap: break-word; font-family: monospace; max-height: 300px; overflow-y: auto; }
        #remote-audio { margin-top: 1em; }
        audio { margin-top: 0.5em; display: block; }
    </style>
    <script src="mediasoup-client.min.js"></script>
</head>
<body>
    <div class="container">
        <h1>Voice Router Test Client</h1>
        
        <div>
            <label for="sessionId">Session ID:</label>
            <input type="text" id="sessionId" placeholder="Enter mediaSessionId here">
        </div>
        
        <button id="btnConnect">Connect</button>
        <button id="btnStartAudio" disabled>Start Audio</button>
        <button id="btnMute" disabled>Mute</button>
        
        <h2>Status Log</h2>
        <div id="status">Not connected.</div>

        <h2>Remote Audio Streams</h2>
        <div id="remote-audio"></div>
    </div>

    <script>
        const txtSessionId = document.getElementById('sessionId');
        const btnConnect = document.getElementById('btnConnect');
        const btnStartAudio = document.getElementById('btnStartAudio');
        const btnMute = document.getElementById('btnMute');
        const statusLog = document.getElementById('status');
        const remoteAudioContainer = document.getElementById('remote-audio');

        let ws;
        let device;
        let sendTransport;
        let recvTransport;
        let producer;
        const consumers = new Map();
        let nextRequestId = 1;
        const pendingRequests = new Map();

        function log(message) {
            console.log(message);
            statusLog.innerHTML = `${new Date().toLocaleTimeString()}: ${message}\n` + statusLog.innerHTML;
        }

        // --- WebSocket Communication ---
        function request(method, data = {}) {
            return new Promise((resolve, reject) => {
                const requestId = nextRequestId++;
                pendingRequests.set(requestId, { resolve, reject });
                
                const message = { id: requestId, method, data };
                ws.send(JSON.stringify(message));
            });
        }

        btnConnect.onclick = () => {
            const sessionId = txtSessionId.value;
            if (!sessionId) {
                alert('Please enter a Session ID.');
                return;
            }

            const wsUrl = `ws://localhost:3001?sessionId=${sessionId}`;
            ws = new WebSocket(wsUrl);

            ws.onopen = () => {
                log('WebSocket connection established.');
                btnConnect.disabled = true;
                connectAndLoadDevice();
            };

            ws.onmessage = (event) => {
                const message = JSON.parse(event.data);
                
                if (message.type === 'response') {
                    const promise = pendingRequests.get(message.id);
                    if (promise) {
                        if (message.error) {
                            promise.reject(new Error(message.error));
                        } else {
                            promise.resolve(message.data);
                        }
                        pendingRequests.delete(message.id);
                    }
                } else if (message.type === 'notification') {
                    handleNotification(message);
                }
            };

            ws.onerror = (error) => {
                log(`WebSocket error: ${error.message}`);
                console.error('WebSocket error:', error);
            };

            ws.onclose = () => {
                log('WebSocket connection closed.');
                btnConnect.disabled = false;
                btnStartAudio.disabled = true;
                btnMute.disabled = true;
            };
        };

        function handleNotification(notification) {
            log(`Received notification: ${notification.method}`);
            switch (notification.method) {
                case 'newProducer':
                    subscribe(notification.data);
                    break;
                case 'producerPaused':
                    log(`Remote producer ${notification.data.producerId} was paused.`);
                    // You could add UI logic here, e.g., show a "muted" icon.
                    break;
                case 'producerResumed':
                    log(`Remote producer ${notification.data.producerId} was resumed.`);
                    // You could add UI logic here, e.g., hide a "muted" icon.
                    break;
                default:
                    log(`Unknown notification method: ${notification.method}`);
            }
        }

        // --- MediaSoup Logic ---
        async function connectAndLoadDevice() {
            try {
                log('Fetching Router RTP Capabilities...');
                const routerRtpCapabilities = await request('getRouterRtpCapabilities');
                
                log('Creating MediaSoup Device...');
                device = new window.mediasoup.Device();
                await device.load({ routerRtpCapabilities });
                log('Device loaded successfully.');

                await createRecvTransport();
                await createSendTransport();

                btnStartAudio.disabled = false;
            } catch (error) {
                log(`Error loading device: ${error.message}`);
                console.error(error);
            }
        }

        async function createSendTransport() {
            log('Creating send transport...');
            const transportInfo = await request('createWebRtcTransport');
            sendTransport = device.createSendTransport(transportInfo);

            sendTransport.on('connect', async ({ dtlsParameters }, callback, errback) => {
                try {
                    log('Connecting send transport...');
                    await request('connectWebRtcTransport', { transportId: sendTransport.id, dtlsParameters });
                    callback();
                    log('Send transport connected.');
                } catch (error) {
                    errback(error);
                }
            });

            sendTransport.on('produce', async ({ kind, rtpParameters }, callback, errback) => {
                try {
                    log('Producing media...');
                    const { id } = await request('produce', {
                        transportId: sendTransport.id,
                        kind,
                        rtpParameters,
                    });
                    callback({ id });
                    log('Media production successful.');
                } catch (error) {
                    errback(error);
                }
            });
        }

        async function createRecvTransport() {
            log('Creating receive transport...');
            const transportInfo = await request('createWebRtcTransport');
            recvTransport = device.createRecvTransport(transportInfo);

            recvTransport.on('connect', async ({ dtlsParameters }, callback, errback) => {
                try {
                    log('Connecting receive transport...');
                    await request('connectWebRtcTransport', { transportId: recvTransport.id, dtlsParameters });
                    callback();
                    log('Receive transport connected.');
                } catch (error) {
                    errback(error);
                }
            });
        }

        btnStartAudio.onclick = async () => {
            try {
                log('Requesting microphone access...');
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                const track = stream.getAudioTracks()[0]; 
                
                log('Starting audio production...');
                producer = await sendTransport.produce({ track });
                btnStartAudio.disabled = true;
                btnMute.disabled = false; // Enable mute button
            } catch (error) {
                log(`Error starting audio: ${error.message}`);
                console.error(error);
            }
        };

        btnMute.onclick = async () => {
            if (!producer) return;

            if (producer.paused) {
                log('Resuming (unmuting) audio...');
                await producer.resume();
                await request('resumeProducer', { producerId: producer.id });
                log('Audio resumed.');
                btnMute.textContent = 'Mute';
                btnMute.classList.remove('mute');
            } else {
                log('Pausing (muting) audio...');
                await producer.pause();
                await request('pauseProducer', { producerId: producer.id });
                log('Audio paused.');
                btnMute.textContent = 'Unmute';
                btnMute.classList.add('mute');
            }
        };

        async function subscribe({ producerId, participantId }) {
            try {
                log(`Subscribing to new producer ${producerId} from participant ${participantId}...`);
                const consumerInfo = await request('consume', {
                    transportId: recvTransport.id,
                    producerId,
                    rtpCapabilities: device.rtpCapabilities,
                });

                const consumer = await recvTransport.consume(consumerInfo);
                consumers.set(consumer.id, consumer);

                log(`Successfully consumed producer ${producerId}.`);

                log(`Resuming consumer ${consumer.id}...`);
                await request('resumeConsumer', { consumerId: consumer.id });
                log(`Consumer ${consumer.id} resumed.`);

                const { track } = consumer;
                const audioElement = document.createElement('audio');
                audioElement.id = `audio-${consumer.id}`;
                audioElement.srcObject = new MediaStream([track]);
                audioElement.controls = true; // ✅ ADD THIS LINE
                audioElement.play();
                remoteAudioContainer.appendChild(audioElement);

                consumer.on('trackended', () => {
                    log(`Track ended for consumer ${consumer.id}.`);
                    document.getElementById(audioElement.id)?.remove();
                    consumers.delete(consumer.id);
                });
            } catch (error) {
                log(`Error subscribing to producer: ${error.message}`);
                console.error(error);
            }
        }
    </script>
</body>
</html>