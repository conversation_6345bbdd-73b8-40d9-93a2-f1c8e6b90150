import { Request, Response } from 'express';
import { StartForkBody } from '../../types';

/**
 * Handles the creation of a new session.
 * Responds with the unique ID of the session.
 */
export async function createSession(req: Request, res: Response): Promise<void> {
  try {
    const sessionId = await req.sessionManager.createSession();
    res.status(201).json({ mediaSessionId: sessionId });
  } catch (error) {
    console.error('Error creating call:', error);
    res.status(500).json({ error: 'Failed to create media session' });
  }
}

/**
 * Handles the deletion of a session.
 */
export function deleteSession(req: Request, res: Response): void {
  try {
    const { sessionId } = req.params;
    if (!sessionId) {
      res.status(400).json({ error: 'Session ID is required' });
      return;
    }
    req.sessionManager.deleteSession(sessionId);
    res.status(204).send();
  } catch (error) {
    console.error('Error deleting call:', error);
    res.status(500).json({ error: 'Failed to delete media session' });
  }
}

// /**
//  * Handles the request to start an RTP fork for a session.
//  */
// export async function startFork(req: Request, res: Response): Promise<void> {
//   try {
//     const { sessionId } = req.params;
//     if (!sessionId) {
//       res.status(400).json({ error: 'Session ID is required' });
//       return;
//     }
//     const forkOptions = req.body as StartForkBody;

//     const session = req.sessionManager.getSession(sessionId);
//     if (!session) {
//       res.status(404).json({ error: 'Session not found' });
//       return;
//     }

//     const forkId = await session.startRtpFork(forkOptions);
//     res.status(201).json({ forkId });
//   } catch (error) {
//     console.error('Error starting fork:', error);
//     res.status(500).json({ error: 'Failed to start RTP fork' });
//   }
// }

// /**
//  * Handles the request to stop an RTP fork.
//  * (Placeholder implementation)
//  */
// export function stopFork(req: Request, res: Response): void {
//   try {
//     const { sessionId, forkId } = req.params;
//     if (!sessionId || !forkId) {
//       res.status(400).json({ error: 'Session ID and Fork ID are required' });
//       return;
//     }

//     const session = req.sessionManager.getSession(sessionId);
//     if (!session) {
//       res.status(404).json({ error: 'Session not found' });
//       return;
//     }

//     // TODO: Implement the logic to stop a specific fork within the Session class
//     // session.stopRtpFork(forkId);

//     console.log(`Stopping fork ${forkId} for session ${sessionId}`);
//     res.status(204).send();
//   } catch (error) {
//     console.error('Error stopping fork:', error);
//     res.status(500).json({ error: 'Failed to stop RTP fork' });
//   }
// }