import json
import jwt
from jwt import PyJWKClient
from fastapi import FastAP<PERSON>, Query, WebSocket
from fastapi.middleware.cors import CORSMiddleware

from config import settings
from participant import Participant, ParticipantStatus

app = FastAPI(
    host=settings.host,
    port=settings.port,
)

# CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

jwks_client = PyJWKClient(f"{settings.supabase_project_url}/auth/v1/.well-known/jwks.json")

# In the future we will want to use Redis so that we can scale horizontally
participants: list[Participant] = []

@app.get("/internal/participants")
def get_available_participants(role: str = Query(None), status: str = Query(None)) -> list[Participant]:
    users = []
    for participant in participants:
        if role and participant.role != role:
            continue
        if status and participant.status != status:
            continue

        users.append(participant)

    return users

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket, token: str = Query(None)):
    if token is None:
        await websocket.close(code=4001, reason="Token is required")
        return

    try:
        # Validate JWT token with Supabase JWKs
        signing_key = jwks_client.get_signing_key_from_jwt(token)
        decoded_token = jwt.decode(
            token,
            signing_key.key,
            algorithms=["ES256", "RS256"],  # Supabase uses ES256
            issuer=f"{settings.supabase_project_url}/auth/v1",
            audience="authenticated"
        )

        participant_id = decoded_token["sub"]
        participant_role = decoded_token["app_role"]

        print(f"User {participant_id} connecting as {participant_role}")

    except jwt.InvalidTokenError as e:
        await websocket.close(code=4001, reason=f"Invalid token: {str(e)}")
        return
    except Exception as e:
        await websocket.close(code=4000, reason=f"Authentication failed: {str(e)}")
        return

    # Accept connection only after successful validation
    await websocket.accept()

    participant = Participant(participant_id, participant_role, ParticipantStatus.AVAILABLE)
    participants.append(participant)

    try:
        # Keep the connection alive and handle incoming messages
        while True:
            data = await websocket.receive_text()

            try:
                # Parse JSON message from UI
                message = json.loads(data)

                # Handle update.status messages: {"status": "AVAILABLE"}
                if "status" in message:
                    status_value = message["status"].lower()
                    if status_value in [status.value for status in ParticipantStatus]:
                        participant.status = ParticipantStatus(status_value)

            except json.JSONDecodeError:
                pass  # Ignore non-JSON messages

    except Exception:
        pass # Connection dropped or error occurred

    finally:
        # Set participant as OFFLINE when they disconnect
        participant.status = ParticipantStatus.OFFLINE
    