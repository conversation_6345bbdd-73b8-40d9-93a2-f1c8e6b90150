# Participant Presence Service

This service tracks the availability of operators and translators. It provides a WebSocket connection for participants to update their status.

# REST Endpoints

## GET /internal/partipants?role={role}&status={status}
Returns a list of participants that match the provided role and status.

# WebSocket Endpoints

## /ws
Connects a participant to the service. The participant must provide a valid JWT token in the query string. The token must be signed with the Supabase JWT signing key. The token must contain the following claims:

- `sub`: The participant's ID
- `role`: The participant's role (`operator` or `translator`)
