[tool.poetry]
name = "ai-speech"
version = "0.1.0"
description = "Cortexa Real-Time Voice Translation Service"
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"
packages = [{include = "src"}]

[tool.poetry.dependencies]
python = ">=3.11,<3.14"
cortexa-common = {path = "../shared/cortexa-common", develop = true}
fastapi = "^0.116.1"
uvicorn = {extras = ["standard"], version = "^0.35.0"}
websockets = "^12.0"

prometheus_client = "^0.22.1"
prometheus-fastapi-instrumentator = "^7.1.0"

# S2ST Pipeline Dependencies
deepgram-sdk = "^4.8.1"
openai = "^1.99.9"
numpy = "^1.26.0,<2.0.0"
webrtcvad = "^2.0.10"
pydantic = {extras = ["email"], version = "^2.11.7"}
pydantic-settings = "^2.10.1"
setuptools = "^80.9.0"
faster-whisper = "^1.2.0"
transformers = "^4.55.2"
torch = "^2.8.0"
torchvision = "^0.23.0"
torchaudio = "^2.8.0"
sentencepiece = "^0.2.1"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
