"""
Abstract base classes for voice processing providers.

This module defines the interfaces that all voice processing providers must implement,
enabling easy switching between different API providers and implementations.
"""

from abc import ABC, abstractmethod
from typing import Any
from cortexacommon.logging import get_logger

logger = get_logger(__name__)


class BaseVADProvider(ABC):
    """Abstract base class for Voice Activity Detection providers."""
    
    def __init__(self, **kwargs):
        """Initialize the VAD provider with configuration."""
        self.config = kwargs
        self._initialized = False
    
    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the VAD provider."""
        pass
    
    @abstractmethod
    def is_speech(self, audio_frame: bytes) -> bool:
        """
        Detect if audio frame contains speech.
        
        Args:
            audio_frame: Audio frame bytes
            
        Returns:
            bool: True if speech detected
        """
        pass
    
    @property
    def frame_size(self) -> int:
        """Get the required frame size for VAD processing."""
        return getattr(self, '_frame_size', 480)  # Default 30ms at 16kHz
    
    @property
    def is_initialized(self) -> bool:
        """Check if provider is initialized."""
        return self._initialized


class BaseSTTProvider(ABC):
    """Abstract base class for Speech-to-Text providers."""

    def __init__(self, **kwargs):
        """Initialize the STT provider with configuration."""
        super().__init__()
        self.config = kwargs
        self._initialized = False
    
    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the STT provider."""
        pass
    
    @abstractmethod
    async def transcribe(self, audio_data: bytes) -> tuple[str, float]:
        """
        Transcribe audio to text.
        
        Args:
            audio_data: Audio data bytes
            
        Returns:
            Tuple of (transcribed_text, confidence_score)
        """
        pass
    
    @property
    def is_initialized(self) -> bool:
        """Check if provider is initialized."""
        return self._initialized
    
    async def health_check(self) -> dict[str, Any]:
        """Perform health check on the provider."""
        return {
            "provider": self.__class__.__name__,
            "initialized": self.is_initialized,
            "status": "healthy" if self.is_initialized else "not_initialized"
        }


class BaseTTSProvider(ABC):
    """Abstract base class for Text-to-Speech providers."""

    def __init__(self, **kwargs):
        """Initialize the TTS provider with configuration."""
        super().__init__()
        self.config = kwargs
        self._initialized = False

    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the TTS provider."""
        pass

    @abstractmethod
    async def synthesize(self, text: str) -> bytes | None:
        """
        Synthesize speech from text.

        Args:
            text: Text to synthesize

        Returns:
            Audio bytes or None if synthesis fails
        """
        pass

    @property
    def is_initialized(self) -> bool:
        """Check if provider is initialized."""
        return self._initialized

    async def health_check(self) -> dict[str, Any]:
        """Perform health check on the provider."""
        return {
            "provider": self.__class__.__name__,
            "initialized": self.is_initialized,
            "status": "healthy" if self.is_initialized else "not_initialized"
        }


class BaseTranslationProvider(ABC):
    """Abstract base class for Translation providers."""

    def __init__(self, **kwargs):
        """Initialize the Translation provider with configuration."""
        super().__init__()
        self.config = kwargs
        self._initialized = False

    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the Translation provider."""
        pass

    @abstractmethod
    async def translate(self, text: str) -> str:
        """
        Translate text to target language.

        Args:
            text: Text to translate

        Returns:
            Translated text
        """
        pass

    @property
    def is_initialized(self) -> bool:
        """Check if provider is initialized."""
        return self._initialized

    async def health_check(self) -> dict[str, Any]:
        """Perform health check on the provider."""
        return {
            "provider": self.__class__.__name__,
            "initialized": self.is_initialized,
            "status": "healthy" if self.is_initialized else "not_initialized"
        }
