"""
DeepGram STT provider implementation.
"""

import io
import asyncio
from cortexacommon.logging import get_logger
import wave
from typing import Any

from .base import BaseSTTProvider
from ...core.config import settings

logger = get_logger(__name__)


class DeepGramSTTProvider(BaseSTTProvider):
    """Speech-to-Text provider using DeepGram API."""
    
    def __init__(self,
                 api_key: str | None = None,
                 model: str | None = None,
                 language: str | None = None,
                 **kwargs):
        """
        Initialize DeepGram STT provider.
        
        Args:
            api_key: DeepGram API key
            model: DeepGram model identifier
            language: Language code
            **kwargs: Additional configuration parameters
        """
        super().__init__(**kwargs)
        self.api_key = api_key or settings.deepgram_api_key
        self.model = model or settings.deepgram_model
        self.language = language or settings.deepgram_language
        self._client: object | None = None
    
    async def initialize(self) -> None:
        """Initialize DeepGram client."""
        if not self.api_key:
            logger.error("DeepGram API key not provided")
            self._client = None
            self._initialized = False
            return
        
        try:
            from deepgram import DeepgramClient
            
            self._client = DeepgramClient(api_key=self.api_key)
            self._initialized = True
            logger.info(f"DeepGram STT client initialized with model {self.model}")
        except ImportError:
            logger.error("deepgram-sdk not available")
            self._client = None
            self._initialized = False
        except Exception as e:
            logger.error(f"Failed to initialize DeepGram client: {e}")
            self._client = None
            self._initialized = False
    
    async def transcribe(self, audio_data: bytes) -> tuple[str, float]:
        """
        Transcribe audio to text using DeepGram API.

        Args:
            audio_data: Audio data bytes (16kHz, 16-bit, mono)

        Returns:
            tuple of (transcribed_text, confidence_score)
        """
        if self._client is None:
            return "Transcription not available", 0.0
        
        try:
            from deepgram import PrerecordedOptions
            
            # Configure transcription options
            options = PrerecordedOptions(
                model=self.model,
                language=self.language,
                smart_format=True,
                punctuate=True,
                diarize=False,
                utterances=False,
            )
            
            # Convert raw PCM audio to WAV format for DeepGram
            wav_data = self._create_wav_from_pcm(audio_data)

            # Run transcription
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self._client.listen.rest.v("1").transcribe_file(
                    source={"buffer": wav_data, "mimetype": "audio/wav"},
                    options=options
                )
            )
            
            # Extract text and confidence from response
            if (response and 
                hasattr(response, 'results') and 
                response.results and 
                response.results.channels and 
                len(response.results.channels) > 0):
                
                channel = response.results.channels[0]
                if channel.alternatives and len(channel.alternatives) > 0:
                    alternative = channel.alternatives[0]
                    text = alternative.transcript.strip()
                    confidence = getattr(alternative, 'confidence', 0.0)
                    
                    return text, confidence
            
            return "", 0.0
            
        except Exception as e:
            logger.error(f"DeepGram STT error: {e}")
            return "", 0.0
    
    def _create_wav_from_pcm(self, pcm_data: bytes) -> bytes:
        """
        Create WAV format audio from raw PCM data.

        Args:
            pcm_data: Raw PCM audio data (16-bit, mono, 16kHz)

        Returns:
            WAV format audio bytes
        """
        # Create a BytesIO buffer to write WAV data
        wav_buffer = io.BytesIO()

        # Create WAV file
        with wave.open(wav_buffer, 'wb') as wav_file:
            wav_file.setnchannels(1)  # Mono
            wav_file.setsampwidth(2)  # 16-bit = 2 bytes
            wav_file.setframerate(settings.audio_sample_rate)  # 16kHz
            wav_file.writeframes(pcm_data)

        # Get the WAV data
        wav_buffer.seek(0)
        return wav_buffer.read()

    async def health_check(self) -> dict[str, Any]:
        """Perform health check on the DeepGram provider."""
        base_health = await super().health_check()
        base_health.update({
            "model": self.model,
            "language": self.language,
            "api_key_configured": bool(self.api_key)
        })
        return base_health
