"""Async task orchestration for the S2ST pipeline."""

import async<PERSON>
import json
from cortexacommon.logging import get_logger
from typing import Any

from fastapi import WebSocketDisconnect
from fastapi.websockets import WebSocketState

from src.pipeline.state import ConnectionState, CallState
from src.core.config import settings
from src.schemas.websocket import HeartbeatMessage, PongMessage
from src.pipeline.s2st import S2STProcessor

logger = get_logger(__name__)


async def reader_task(state: ConnectionState) -> None:
    """
    Reader task: receives audio from client and puts it in the inbound queue.
    
    This task continuously reads audio data from the WebSocket connection
    and places it in the inbound queue for processing by the S2ST pipeline.
    
    Args:
        state: Connection state with WebSocket and queues
    """
    logger.info(f"Starting reader task for call {state.call_id}")
    
    try:
        while state.state.value in ["active", "processing"]:
            try:
                # Check if WebSocket is still connected
                if state.websocket.client_state != WebSocketState.CONNECTED:
                    logger.info(f"WebSocket disconnected for call {state.call_id}")
                    break
                
                # Receive data from WebSocket
                data = await state.websocket.receive()
                
                if data["type"] == "websocket.receive":
                    if "bytes" in data:
                        # Audio data received
                        audio_data = data["bytes"]
                        state.add_audio_chunk(audio_data)
                        
                        # Put audio data in processing queue
                        await state.inbound_queue.put(audio_data)
                        
                        logger.debug(f"Audio chunk received: {len(audio_data)} bytes")
                        
                    elif "text" in data:
                        # Control message received
                        try:
                            message = json.loads(data["text"])
                            await handle_control_message(state, message)
                        except json.JSONDecodeError:
                            logger.warning(f"Invalid JSON message received: {data['text']}")

                elif data["type"] == "websocket.disconnect":
                    logger.info(f"WebSocket disconnect message for call {state.call_id}")
                    break

            except WebSocketDisconnect:
                logger.info(f"WebSocket disconnected for call {state.call_id}")
                break

            except Exception as e:
                logger.error(f"Error in reader task for call {state.call_id}: {e}")
                state.add_error("reader_task_error", str(e))
                break

    except Exception as e:
        logger.error(f"Reader task failed for call {state.call_id}: {e}")
        state.add_error("reader_task_fatal_error", str(e))

    finally:
        logger.info(f"Reader task ended for call {state.call_id}")


async def writer_task(state: ConnectionState) -> None:
    """
    Writer task: takes translated audio from outbound queue and sends it to the client.
    
    This task continuously reads processed audio from the outbound queue
    and sends it back to the client via the WebSocket connection.
    
    Args:
        state: Connection state with WebSocket and queues
    """
    logger.info(f"Starting writer task for call {state.call_id}")

    try:
        while state.state.value in ["active", "processing"]:
            try:
                # Get processed audio from outbound queue
                tts_audio_chunk = await asyncio.wait_for(
                    state.outbound_queue.get(),
                    timeout=1.0
                )

                # Check if WebSocket is still connected
                if state.websocket.client_state != WebSocketState.CONNECTED:
                    logger.info(f"WebSocket disconnected, cannot send audio for call {state.call_id}")
                    break

                # Send audio data to client
                await state.websocket.send_bytes(tts_audio_chunk)
                state.outbound_queue.task_done()
                
                logger.debug(f"Audio chunk sent: {len(tts_audio_chunk)} bytes")

            except asyncio.TimeoutError:
                # No audio to send, continue
                continue

            except WebSocketDisconnect:
                logger.info(f"WebSocket disconnected during write for call {state.call_id}")
                break

            except Exception as e:
                logger.error(f"Error in writer task for call {state.call_id}: {e}")
                state.add_error("writer_task_error", str(e))
                break

    except Exception as e:
        logger.error(f"Writer task failed for call {state.call_id}: {e}")
        state.add_error("writer_task_fatal_error", str(e))

    finally:
        logger.info(f"Writer task ended for call {state.call_id}")


async def processor_task(state: ConnectionState, s2st_processor: S2STProcessor | None = None) -> None:
    """
    Processor task: runs the S2ST pipeline to process audio chunks.

    This task manages the Speech-to-Speech Translation pipeline,
    reading from the inbound queue and putting results in the outbound queue.

    Args:
        state: Connection state with audio queues
        s2st_processor: S2ST processor instance (passed from caller to avoid circular imports)
    """
    logger.info(f"Starting processor task for call {state.call_id}")

    try:
        if not s2st_processor or not s2st_processor.is_initialized:
            logger.error(f"S2ST processor not available for call {state.call_id}")
            state.add_error("processor_not_available", "S2ST processor not initialized")
            return

        # Run the main processing loop
        await s2st_processor.process_audio_chunk(state)

    except Exception as e:
        logger.error(f"Processor task failed for call {state.call_id}: {e}")
        state.add_error("processor_task_fatal_error", str(e))

    finally:
        logger.info(f"Processor task ended for call {state.call_id}")


async def heartbeat_task(state: ConnectionState) -> None:
    """
    Heartbeat task: sends periodic heartbeat messages to keep connection alive.
    
    Args:
        state: Connection state with WebSocket
    """
    logger.info(f"Starting heartbeat task for call {state.call_id}")

    try:
        while state.state.value in ["active", "processing"]:
            try:
                # Wait for heartbeat interval
                await asyncio.sleep(settings.ws_heartbeat_interval)
    
                # Check if WebSocket is still connected
                if state.websocket.client_state != WebSocketState.CONNECTED:
                    break

                # Send heartbeat message
                await state.websocket.send_json(HeartbeatMessage(
                    call_id=state.call_id,
                    timestamp=state.last_activity,
                ).model_dump())

                logger.debug(f"Heartbeat sent for call {state.call_id}")
    
            except WebSocketDisconnect:
                logger.info(f"WebSocket disconnected during heartbeat for call {state.call_id}")
                break

            except Exception as e:
                logger.error(f"Error in heartbeat task for call {state.call_id}: {e}")
                state.add_error("heartbeat_task_error", str(e))
                break

    except Exception as e:
        logger.error(f"Heartbeat task failed for call {state.call_id}: {e}")
        state.add_error("heartbeat_task_fatal_error", str(e))

    finally:
        logger.info(f"Heartbeat task ended for call {state.call_id}")


async def handle_control_message(state: ConnectionState, message: dict[str, Any]) -> None:
    """
    Handle control messages from the client.
    
    Args:
        state: Connection state
        message: Control message from client
    """
    message_type = message.get("type")
    
    if message_type == "ping":
        # Respond to ping with pong
        await state.websocket.send_json(PongMessage(
            call_id=state.call_id,
            timestamp=state.last_activity,
        ).model_dump())

    elif message_type == "config_update":
        # Update configuration
        config_data = message.get("config", {})
        state.config.update(config_data)
        logger.info(f"Configuration updated for call {state.call_id}: {config_data}")

    elif message_type == "end_call":
        # Client requested to end the call
        logger.info(f"Call end requested by client for call {state.call_id}")
        state.state = CallState.ENDING

    else:
        logger.warning(f"Unknown control message type: {message_type}")


async def run_pipeline(state: ConnectionState, s2st_processor: S2STProcessor | None = None) -> None:
    """
    Main orchestrator: runs all pipeline tasks concurrently.

    This function starts and manages all the concurrent tasks needed
    for real-time voice translation:
    - Reader task: receives audio from client
    - Writer task: sends translated audio to client
    - Processor task: runs S2ST pipeline
    - Heartbeat task: keeps connection alive

    Args:
        state: Connection state with all necessary information
        s2st_processor: S2ST processor instance (passed to avoid circular imports)
    """
    logger.info(f"Starting pipeline orchestration for call {state.call_id}")

    # Create all tasks
    tasks = [
        asyncio.create_task(reader_task(state), name=f"reader-{state.call_id}"),
        asyncio.create_task(writer_task(state), name=f"writer-{state.call_id}"),
        asyncio.create_task(processor_task(state, s2st_processor), name=f"processor-{state.call_id}"),
        asyncio.create_task(heartbeat_task(state), name=f"heartbeat-{state.call_id}"),
    ]
    
    try:
        # Wait for any task to complete or fail
        done, pending = await asyncio.wait(
            tasks,
            return_when=asyncio.FIRST_COMPLETED
        )

        # Cancel remaining tasks
        for task in pending:
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass
    
        # Check if any task failed
        for task in done:
            try:
                await task
            except Exception as e:
                logger.error(f"Task {task.get_name()} failed: {e}")
                state.add_error("task_failure", str(e), {"task_name": task.get_name()})

    except Exception as e:
        logger.error(f"Pipeline orchestration failed for call {state.call_id}: {e}")
        state.add_error("pipeline_orchestration_error", str(e))

        # Cancel all tasks
        for task in tasks:
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass

    finally:
        logger.info(f"Pipeline orchestration ended for call {state.call_id}")
        state.state = CallState.ENDING
