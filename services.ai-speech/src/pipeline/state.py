import asyncio
import time
from dataclasses import dataclass, field
from enum import Enum
from typing import Any

from fastapi import WebSocket

from cortexacommon.security.gateway import AuthenticatedUser


class CallState(str, Enum):
    """Call state enumeration."""
    CONNECTING = "connecting"
    ACTIVE = "active"
    PROCESSING = "processing"
    ENDING = "ending"
    ENDED = "ended"
    ERROR = "error"


class AudioSegment:
    """Represents a processed audio segment with metadata."""
    
    def __init__(
        self,
        audio_data: bytes,
        timestamp: float,
        duration: float,
        sample_rate: int = 16000,
        channels: int = 1,
    ):
        self.audio_data = audio_data
        self.timestamp = timestamp
        self.duration = duration
        self.sample_rate = sample_rate
        self.channels = channels
        self.processed = False
        self.transcript: str | None = None
        self.translation: str | None = None
        self.tts_audio: bytes | None = None


@dataclass
class TranscriptEntry:
    """Represents a transcript entry with timing information."""
    timestamp: float
    original_text: str
    translated_text: str | None = None
    speaker: str = "user"
    confidence: float = 0.0


@dataclass
class ConnectionState:
    """Manages state for a single WebSocket connection."""

    # Connection information
    websocket: WebSocket
    call_id: str

    # Authentication information (provided by gateway)
    authenticated_user: AuthenticatedUser | None = None

    # Connection metadata
    connected_at: float = field(default_factory=time.time)
    last_activity: float = field(default_factory=time.time)
    state: CallState = CallState.CONNECTING
    
    # Audio processing buffers
    audio_buffer: bytearray = field(default_factory=bytearray)
    processed_segments: list[AudioSegment] = field(default_factory=list)
    
    # Async queues for pipeline communication
    inbound_queue: asyncio.Queue = field(default_factory=lambda: asyncio.Queue(maxsize=100))
    outbound_queue: asyncio.Queue = field(default_factory=lambda: asyncio.Queue(maxsize=100))
    
    # Transcript and translation data
    transcript: list[TranscriptEntry] = field(default_factory=list)
    
    # Pipeline state
    is_processing: bool = False
    last_vad_activity: float = 0.0
    silence_duration: float = 0.0
    
    # Statistics
    total_audio_received: int = 0
    total_audio_sent: int = 0
    segments_processed: int = 0
    
    # Configuration
    config: dict[str, Any] = field(default_factory=dict)

    # Error tracking
    errors: list[dict[str, Any]] = field(default_factory=list)
    
    def update_activity(self):
        """Update last activity timestamp."""
        self.last_activity = time.time()
    
    def add_audio_chunk(self, chunk: bytes) -> None:
        """Add audio chunk to buffer."""
        self.audio_buffer.extend(chunk)
        self.total_audio_received += len(chunk)
        self.update_activity()
    
    def get_audio_segment(self, length: int) -> bytes | None:
        """Extract audio segment from buffer."""
        if len(self.audio_buffer) >= length:
            segment = bytes(self.audio_buffer[:length])
            self.audio_buffer = self.audio_buffer[length:]
            return segment
        return None
    
    def clear_audio_buffer(self) -> None:
        """Clear the audio buffer."""
        self.audio_buffer.clear()
    
    def add_transcript_entry(
        self,
        text: str,
        translated_text: str | None = None,
        confidence: float = 0.0,
    ) -> None:
        """Add entry to transcript."""
        entry = TranscriptEntry(
            timestamp=time.time(),
            original_text=text,
            translated_text=translated_text,
            confidence=confidence,
        )
        self.transcript.append(entry)
    
    def add_error(self, error_type: str, message: str, details: dict | None = None) -> None:
        """Add error to error log."""
        error_entry = {
            "timestamp": time.time(),
            "type": error_type,
            "message": message,
            "details": details or {},
        }
        self.errors.append(error_entry)
    
    def get_connection_duration(self) -> float:
        """Get connection duration in seconds."""
        return time.time() - self.connected_at
    
    def get_idle_time(self) -> float:
        """Get idle time since last activity."""
        return time.time() - self.last_activity
    
    def is_idle(self, timeout: float = 300.0) -> bool:
        """Check if connection is idle beyond timeout."""
        return self.get_idle_time() > timeout
    
    def get_stats(self) -> dict[str, Any]:
        """Get connection statistics."""
        stats = {
            "call_id": self.call_id,
            "state": self.state.value,
            "connected_at": self.connected_at,
            "duration": self.get_connection_duration(),
            "idle_time": self.get_idle_time(),
            "total_audio_received": self.total_audio_received,
            "total_audio_sent": self.total_audio_sent,
            "segments_processed": self.segments_processed,
            "transcript_entries": len(self.transcript),
            "errors": len(self.errors),
            "buffer_size": len(self.audio_buffer),
            "inbound_queue_size": self.inbound_queue.qsize(),
            "outbound_queue_size": self.outbound_queue.qsize(),
        }

        # Add user information if authenticated
        if self.authenticated_user:
            stats["user"] = {
                "user_id": self.authenticated_user.user_id,
                "email": self.authenticated_user.email,
            }

        return stats
    
    async def cleanup(self) -> None:
        """Clean up connection resources."""
        self.state = CallState.ENDED
        
        # Clear queues
        while not self.inbound_queue.empty():
            try:
                self.inbound_queue.get_nowait()
                self.inbound_queue.task_done()
            except asyncio.QueueEmpty:
                break
        
        while not self.outbound_queue.empty():
            try:
                self.outbound_queue.get_nowait()
                self.outbound_queue.task_done()
            except asyncio.QueueEmpty:
                break
        
        # Clear buffers
        self.clear_audio_buffer()
        self.processed_segments.clear()


class ConnectionStateManager:
    """Manages multiple connection states."""
    
    def __init__(self):
        self._connections: dict[str, ConnectionState] = {}
        self._lock = asyncio.Lock()
    
    async def add_connection(self, state: ConnectionState) -> None:
        """Add a new connection state."""
        async with self._lock:
            self._connections[state.call_id] = state
    
    async def remove_connection(self, call_id: str) -> ConnectionState | None:
        """Remove and return connection state."""
        async with self._lock:
            return self._connections.pop(call_id, None)
    
    async def get_connection(self, call_id: str) -> ConnectionState | None:
        """Get connection state by call ID."""
        async with self._lock:
            return self._connections.get(call_id)
    
    async def get_all_connections(self) -> list[ConnectionState]:
        """Get all active connections."""
        async with self._lock:
            return list(self._connections.values())
    
    async def get_connection_count(self) -> int:
        """Get number of active connections."""
        async with self._lock:
            return len(self._connections)
    
    async def cleanup_idle_connections(self, timeout: float = 300.0) -> list[str]:
        """Clean up idle connections and return their call IDs."""
        idle_call_ids = []
        async with self._lock:
            for call_id, state in list(self._connections.items()):
                if state.is_idle(timeout):
                    idle_call_ids.append(call_id)
                    await state.cleanup()
                    del self._connections[call_id]
        return idle_call_ids



