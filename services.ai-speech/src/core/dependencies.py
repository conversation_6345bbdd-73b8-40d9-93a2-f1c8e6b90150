"""
FastAPI dependency injection functions for the ai-speech service.

This module provides dependency injection functions that can be used
with FastAPI's Depends() to inject shared resources into endpoints.
"""

from fastapi import Depends

from .context import get_app_context, ApplicationContext
from ..pipeline.s2st import S2STProcessor


async def get_app_context_dependency() -> ApplicationContext:
    """
    FastAPI dependency to get the application context.
    
    Returns:
        ApplicationContext: The application context instance
    """
    return await get_app_context()


async def get_s2st_processor(
    app_context: ApplicationContext = Depends(get_app_context_dependency)
) -> S2STProcessor | None:
    """
    FastAPI dependency to get the S2ST processor instance.

    Args:
        app_context: The application context (injected by FastAPI)

    Returns:
        S2STProcessor | None: The S2ST processor instance if available
    """
    return app_context.s2st_processor
