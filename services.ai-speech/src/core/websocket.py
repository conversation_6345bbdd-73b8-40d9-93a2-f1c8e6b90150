from fastapi import WebSocket, WebSocketDisconnect
from fastapi.websockets import WebSocketState

from cortexacommon.logging import get_logger
from cortexacommon.security.gateway import AuthenticatedUser

from src.pipeline.state import ConnectionState, CallState
from src.pipeline.tasks import run_pipeline
from src.schemas.events import CallEndedEvent, CallStartedEvent
from src.schemas.websocket import CallEndedMessage, ConnectionEstablishedMessage
from src.core.context import ApplicationContext


logger = get_logger(__name__)


class WebSocketUnauthenticatedError(Exception):
    """Exception raised when WebSocket authentication fails."""


class WebSocketHandler():
    """WebSocket handler with tracing capabilities."""

    def __init__(self, context: ApplicationContext):
        self.context = context
        super().__init__()

    async def handle_websocket(self, websocket: WebSocket, call_id: str) -> None:
        try:
            await websocket.accept()

            authenticated_user = await self.authenticate(websocket)
            logger.info(f"Authenticated user {authenticated_user.user_id} connecting to call {call_id}")

            await self.process_state(websocket, call_id, authenticated_user)
            
        except WebSocketUnauthenticatedError:     
            await websocket.close(code=4001, reason="Authentication required")
            logger.warning(f"Connection rejected for call {call_id}: no authentication provided")

    async def authenticate(self, websocket: WebSocket) -> AuthenticatedUser:
        headers_dict = dict(websocket.headers)
        authenticated_user = self.context.gateway_authenticator.parse_user_from_headers(headers_dict)
        if not authenticated_user:
            raise WebSocketUnauthenticatedError("Authentication required")

        return authenticated_user

    async def process_state(self, websocket: WebSocket, call_id: str, authenticated_user: AuthenticatedUser) -> None:
        """Implementation of WebSocket connection handling."""
        state = ConnectionState(
            websocket=websocket,
            call_id=call_id,
            authenticated_user=authenticated_user,
            state=CallState.ACTIVE,
        )

        await self.context.connection_state_manager.add_connection(state)

        # Update metrics
        self.context.metrics.record_connection_opened()

        # Kafka event for call started
        await self.context.events_publisher.publish_event(
            event=CallStartedEvent(
                call_id=call_id,
            )
        )

        try:
            # Send initial connection confirmation
            await websocket.send_json(ConnectionEstablishedMessage(
                call_id=call_id,
                message="Voice translation session started",
                timestamp=state.connected_at,
            ).model_dump())

            await run_pipeline(state, self.context.s2st_processor)

        except WebSocketDisconnect:
            logger.info(f"Client for call {call_id} disconnected")
            state.state = CallState.ENDING
        except Exception as e:
            logger.error(f"Error in WebSocket pipeline for call {call_id}: {str(e)}")
            state.state = CallState.ERROR
            state.add_error("pipeline_error", str(e))
        finally:
            await self.process_termination(state)

    async def process_termination(self, state: ConnectionState) -> None:
        """ Handle call termination and cleanup. """
        logger.info(f"Terminating call {state.call_id}")
        
        try:
            # Update state
            state.state = CallState.ENDING
            
            # Publish CallEnded event to Kafka
            await self.context.events_publisher.publish_event(
                event=CallEndedEvent(
                    call_id=state.call_id,
                    duration_seconds=state.get_connection_duration(),
                )
            )

            # Update metrics
            self.context.metrics.record_connection_closed()
            
            # Send final message to client if connection is still open
            if state.websocket.client_state == WebSocketState.CONNECTED:
                try:
                    await state.websocket.send_json(CallEndedMessage(
                        call_id=state.call_id,
                        message="Call ended successfully",
                        stats=state.get_stats(),
                    ).model_dump())
                except Exception:
                    pass  # Connection might be closed
            
        except Exception as e:
            logger.error(f"Error during call termination for {state.call_id}: {str(e)}")
            state.add_error("termination_error", str(e))
        
        finally:
            await state.cleanup()
            await self.context.connection_state_manager.remove_connection(state.call_id)
            state.state = CallState.ENDED
            
            logger.info(f"Call {state.call_id} terminated and cleaned up")
