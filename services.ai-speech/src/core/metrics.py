from fastapi import FastAP<PERSON>
from prometheus_client import Counter, Gauge
from prometheus_fastapi_instrumentator import Instrumentator

from cortexacommon.logging import get_logger

from .config import settings

logger = get_logger(__name__)


class Metrics:
    """Helper class for collecting custom metrics."""

    def __init__(self):
        """Initialize metrics."""
        self.settings = settings
        if not settings.monitoring.metrics_enabled:
            logger.info("Metrics disabled, skipping initialization")
            return

        # Custom metrics
        self.total_connections = Counter(
            "voice_gateway_total_connections",
            "Total number of WebSocket connections",
        )

        self.active_connections = Gauge(
            "voice_gateway_active_connections",
            "Number of active WebSocket connections",
        )

    @staticmethod
    def setup_fastapi_metrics(app: FastAPI) -> None:
        # Initialize FastAPI instrumentator
        instrumentator = Instrumentator(
            excluded_handlers=["/metrics"],
            should_group_status_codes=True,
            should_ignore_untemplated=True,
            should_instrument_requests_inprogress=True,
            inprogress_name="http_requests_inprogress",
            inprogress_labels=True,
        )
        instrumentator.instrument(app, metric_subsystem="voice_gateway")

        logger.info("Exposing metrics endpoint at /metrics...")
        instrumentator.expose(app, endpoint="/metrics")

        logger.info("Prometheus metrics initialized successfully")
    
    def record_connection_opened(self):
        """Record a new WebSocket connection."""
        if self.settings.monitoring.metrics_enabled:
            self.total_connections.inc()
            self.active_connections.inc()
    
    def record_connection_closed(self):
        """Record a closed WebSocket connection."""
        if self.settings.monitoring.metrics_enabled:
            self.active_connections.dec()
