services:

  # ------------------------------------------------------------------
  # Cortexa Microservices Platform
  # ------------------------------------------------------------------

  user-management:
    build: ./services.user-management
    container_name: cortexa-user-management
    ports:
      - "8000:8000"
    env_file: ./services.user-management/.env
    environment:
      - DEBUG=true
      - HOST=0.0.0.0
      - PORT=8000
      - SUPABASE_PROJECT_URL=http://host.docker.internal:54321
    extra_hosts:
      - "host.docker.internal:host-gateway"
    networks:
      - cortexa-network
    labels:
      - "traefik.enable=true"
      # Define the router and its rule
      - "traefik.http.routers.user-management.rule=Host(`localhost`) && PathPrefix(`/users`)"
      - "traefik.http.routers.user-management.entrypoints=web"
      # Link the router to the service Traefik will create
      - "traefik.http.routers.user-management.service=user-management-service"
      # Apply your administrator role-check middleware (temporarily disabled for testing)
      - "traefik.http.routers.user-management.middlewares=jwt-auth-role-administrator@file"
      # Define the service and tell Traefik which port the container is listening on
      - "traefik.http.services.user-management-service.loadbalancer.server.port=8000"

  # voice-router:
  #   build:
  #     context: .
  #     dockerfile: services.voice-router/Dockerfile
  #   container_name: cortexa-voice-router
  #   ports:
  #     - "3002:3000"      # HTTP API
  #     # RTC ports are handled internally by mediasoup and don't need external mapping
  #   environment:
  #     - LISTEN_HOST=0.0.0.0
  #     - LISTEN_PORT=3000
  #     - MEDIASOUP_WORKERS=4
  #     - NODE_ENV=production
  #   networks:
  #     - cortexa-network
  #   labels:
  #     - "traefik.enable=true"
  #     - "traefik.http.routers.voice-router.rule=PathPrefix(`/voice-router`)"
  #     - "traefik.http.routers.voice-router.entrypoints=web"
  #     - "traefik.http.services.voice-router.loadbalancer.server.port=3000"
  #     - "traefik.http.middlewares.voice-router-stripprefix.stripprefix.prefixes=/voice-router"
  #     - "traefik.http.routers.voice-router.middlewares=voice-router-stripprefix"
  #   healthcheck:
  #     test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3
  #     start_period: 10s

  # =========== sidecars ===========

  # ------------------------------------------------------------------
  # Traefik Reverse Proxy
  #
  # What: Reverse proxy for all services, provides load balancing, SSL termination, and JWT authentication
  # Why: Centralized point of entry for all services, simplifies service discovery and routing
  # ------------------------------------------------------------------

  traefik:
    image: "traefik:v2.11"
    container_name: cortexa-traefik
    command:
      # Enable the API dashboard (for debugging)
      - "--api.insecure=true"
      # Enable the Docker provider to read service labels
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      # Point to the dynamic configuration file for our middleware
      - "--providers.file.directory=/configuration/"
      - "--providers.file.watch=true"
      # Define the entrypoint for web traffic
      - "--entrypoints.web.address=:80"
      # Enable the experimental plugin feature and specify the JWT middleware
      - "--experimental.plugins.jwt.modulename=github.com/agilezebra/jwt-middleware"
      - "--experimental.plugins.jwt.version=v1.3.2"
    ports:
      - "80:80"      # Public-facing port
      - "8080:8080"  # Traefik dashboard
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock:ro"
      - "./infra/gateway:/configuration/"
    networks:
      - cortexa-network

  # ------------------------------------------------------------------
  # Databases
  # ------------------------------------------------------------------
  
  postgres:
    image: postgres:15.4-alpine
    container_name: cortexa-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=cortexa
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - cortexa-network

  # ------------------------------------------------------------------
  # Kafka and Zookeeper
  #
  # What: Message queue for inter-service communication
  # Why: Decouples services, enables asynchronous communication, and provides fault tolerance
  # ------------------------------------------------------------------

  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: cortexa-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - cortexa-network

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: cortexa-kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
    healthcheck:
      test: ["CMD", "kafka-topics", "--bootstrap-server", "localhost:9092", "--list"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - cortexa-network

  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: cortexa-kafka-ui
    depends_on:
      - kafka
    ports:
      - "8081:8080"
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
    networks:
      - cortexa-network

  # ------------------------------------------------------------------
  # Monitoring and Tracing
  #
  # What: Observability tools for monitoring and tracing
  # Why: Provides insights into service health, performance, and dependencies
  # ------------------------------------------------------------------

  prometheus:
    image: prom/prometheus:v2.47.0
    container_name: cortexa-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./infra/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - cortexa-network

  grafana:
    image: grafana/grafana:10.1.0
    container_name: cortexa-grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      #- ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      #- ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    networks:
      - cortexa-network


volumes:
  postgres_data:
  prometheus_data:
  grafana_data:


networks:
  cortexa-network:
    driver: bridge
