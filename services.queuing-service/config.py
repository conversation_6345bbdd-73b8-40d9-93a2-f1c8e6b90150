"""
Configuration settings for the queuing service.

This module defines all configuration settings using Pydantic settings
with environment variable support.
"""

from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings for the queuing service."""
    
    # Service configuration
    service_name: str = "queuing-service"
    host: str = "0.0.0.0"
    port: int = 8003
    debug: bool = False
    
    # Kafka configuration
    kafka_bootstrap_servers: str = "localhost:9092"
    kafka_group_id: str = "queuing-service"
    
    # External service URLs
    participant_presence_url: str = "http://localhost:8001"
    
    # Queue configuration
    max_queue_size: int = 1000
    queue_timeout_seconds: int = 300  # 5 minutes
    assignment_timeout_seconds: int = 30
    
    # Routing configuration (FIFO only)
    routing_strategy: str = "fifo"
    
    # Health check configuration
    health_check_interval_seconds: int = 30
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )


settings = Settings()
