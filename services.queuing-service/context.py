import asyncio

from cortexacommon.events.consumer import EventConsumer
from cortexacommon.events.producer import EventProducer
from cortexacommon.config import KafkaSettings

from config import settings
from services.participant_presence_client import ParticipantPresenceClient
from services.queue_manager import QueueManager
from services.routing_engine import RoutingEngine


class ApplicationContext:
    """
    Application context for managing shared resources.
    
    This class provides a singleton pattern for managing application-wide
    resources like Kafka producers/consumers and external service clients.
    """
    
    _instance: 'ApplicationContext | None' = None
    _lock = asyncio.Lock()
    
    def __init__(self):
        """Initialize the application context."""
        self._event_producer: EventProducer | None = None
        self._event_consumer: EventConsumer | None = None
        self._participant_presence_client: ParticipantPresenceClient | None = None
        self._queue_manager: QueueManager | None = None
        self._routing_engine: RoutingEngine | None = None
        self._initialized = False
    
    @classmethod
    async def get_instance(cls) -> 'ApplicationContext':
        """
        Get the singleton instance of the application context.
        
        Returns:
            ApplicationContext: The singleton instance
        """
        if cls._instance is None:
            async with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance
    
    @property
    def event_producer(self) -> EventProducer:
        """Get the Kafka event producer instance."""
        assert self._event_producer is not None, "Event producer not initialized"
        return self._event_producer
    
    @property
    def event_consumer(self) -> EventConsumer:
        """Get the Kafka event consumer instance."""
        assert self._event_consumer is not None, "Event consumer not initialized"
        return self._event_consumer
    
    @property
    def participant_presence_client(self) -> ParticipantPresenceClient:
        """Get the participant presence client instance."""
        assert self._participant_presence_client is not None, "Participant presence client not initialized"
        return self._participant_presence_client
    
    @property
    def queue_manager(self) -> QueueManager:
        """Get the queue manager instance."""
        assert self._queue_manager is not None, "Queue manager not initialized"
        return self._queue_manager
    
    @property
    def routing_engine(self) -> RoutingEngine:
        """Get the routing engine instance."""
        assert self._routing_engine is not None, "Routing engine not initialized"
        return self._routing_engine
    
    @property
    def is_initialized(self) -> bool:
        """Check if the application context is initialized."""
        return self._initialized
    
    async def initialize(self) -> None:
        """
        Initialize the application context and its resources.
        
        This method initializes all shared resources including Kafka
        producers/consumers and external service clients.
        """
        if self._initialized:
            return
        
        try:
            # Initialize Kafka settings
            kafka_settings = KafkaSettings(
                bootstrap_servers=settings.kafka_bootstrap_servers,
                group_id=settings.kafka_group_id
            )
            
            # Initialize event producer
            self._event_producer = EventProducer(kafka_settings)
            await self._event_producer.start()
            
            # Initialize event consumer for call.started events
            self._event_consumer = EventConsumer(
                kafka_settings=kafka_settings,
                topics=["call-controller.call-started"],
                group_id=settings.kafka_group_id
            )
            await self._event_consumer.start()
            
            # Initialize participant presence client
            self._participant_presence_client = ParticipantPresenceClient(
                base_url=settings.participant_presence_url
            )
            
            # Test the connection to participant presence service
            try:
                await self._participant_presence_client.get_available_participants()
            except Exception as e:
                raise RuntimeError(f"Failed to connect to participant presence service: {e}")
            
            # Initialize queue manager
            self._queue_manager = QueueManager(
                max_size=settings.max_queue_size,
                timeout_seconds=settings.queue_timeout_seconds
            )
            
            # Initialize routing engine
            self._routing_engine = RoutingEngine(
                participant_client=self._participant_presence_client
            )
            
            self._initialized = True
            
        except Exception as e:
            self._initialized = False
            raise RuntimeError(f"Failed to initialize application context: {e}")

    async def cleanup(self) -> None:
        """
        Clean up the application context and its resources.
        
        This method should be called during application shutdown.
        """
        try:
            if self._event_consumer:
                await self._event_consumer.stop()
                self._event_consumer = None
                
            if self._event_producer:
                await self._event_producer.stop()
                self._event_producer = None
                
            if self._participant_presence_client:
                await self._participant_presence_client.close()
                self._participant_presence_client = None
                
            if self._queue_manager:
                await self._queue_manager.cleanup()
                self._queue_manager = None
                
            self._routing_engine = None
            self._initialized = False
            
        except Exception as e:
            # Log error but don't raise to avoid issues during shutdown
            print(f"Error during application context cleanup: {e}")
    
    @classmethod
    async def reset_instance(cls) -> None:
        """
        Reset the singleton instance (mainly for testing).
        
        This method should only be used in test scenarios.
        """
        async with cls._lock:
            if cls._instance:
                await cls._instance.cleanup()
            cls._instance = None


# Global function to get the application context
async def get_app_context() -> ApplicationContext:
    """
    Get the application context instance.
    
    Returns:
        ApplicationContext: The application context instance
    """
    return await ApplicationContext.get_instance()
