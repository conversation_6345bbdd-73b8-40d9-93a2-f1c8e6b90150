from fastapi import APIRouter, Depends, HTTPException
from uuid import UUID

from context import ApplicationContext
from dependencies import get_app_context_dependency
from models import ParticipantRole, QueueStats
from .queue_models import QueuePositionResponse

router = APIRouter(
    prefix="/queue",
    tags=["queue"],
    responses={404: {"description": "Not found"}},
)


@router.get(
    "/status",
    response_model=QueueStats,
    summary="Get queue status",
    description="Get current queue status including statistics and available participants"
)
async def get_queue_status(
    context: ApplicationContext = Depends(get_app_context_dependency)
) -> QueueStats:
    """
    Get current queue status and statistics.
    
    This endpoint provides comprehensive information about the current
    state of the call queue, including:
    - Total number of queued calls
    - Breakdown by priority and role
    - Average and longest wait times
    - Available participant counts
    
    Returns:
        QueueStatusResponse: Current queue status and statistics
    """
    try:
        # Get available participant counts
        participant_counts = await context.routing_engine.get_available_participants_count()
        
        # Get queue status with participant counts
        status = await context.queue_manager.get_queue_status(
            available_operators=participant_counts.get(ParticipantRole.OPERATOR, 0),
            available_translators=participant_counts.get(ParticipantRole.TRANSLATOR, 0)
        )
        
        return status
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get queue status: {str(e)}"
        )


@router.get(
    "/position/{call_id}",
    response_model=QueuePositionResponse,
    summary="Get call position in queue",
    description="Get the current position of a specific call in the queue"
)
async def get_call_position(
    call_id: UUID,
    context: ApplicationContext = Depends(get_app_context_dependency)
) -> QueuePositionResponse:
    """
    Get the position of a specific call in the queue.
    
    Args:
        call_id: The UUID of the call to check
        context: Application context (injected by FastAPI)
        
    Returns:
        Dictionary containing the call's position in the queue
        
    Raises:
        HTTPException: If the call is not found in the queue
    """
    try:
        position = await context.queue_manager.get_queue_position(call_id)
        
        if position is None:
            raise HTTPException(
                status_code=404,
                detail=f"Call {call_id} not found in queue"
            )
        
        return QueuePositionResponse(
            call_id=call_id,
            queue_position=position,
            message=f"Call is at position {position} in the queue"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get call position: {str(e)}"
        )
