# Service Configuration
SERVICE_NAME=queuing-service
HOST=0.0.0.0
PORT=8003
DEBUG=false

# Kafka Configuration
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_GROUP_ID=queuing-service

# External Service URLs
PARTICIPANT_PRESENCE_URL=http://localhost:8001

# Queue Configuration
MAX_QUEUE_SIZE=1000
QUEUE_TIMEOUT_SECONDS=300
ASSIGNMENT_TIMEOUT_SECONDS=30

# Routing Configuration (FIFO only)
ROUTING_STRATEGY=fifo

# Health Check Configuration
HEALTH_CHECK_INTERVAL_SECONDS=30
