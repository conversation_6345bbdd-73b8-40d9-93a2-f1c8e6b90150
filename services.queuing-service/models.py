from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any
from uuid import UUID

from pydantic import BaseModel, Field


class CallPriority(str, Enum):
    """Call priority levels for priority-based routing."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    CRITICAL = "critical"


class RoutingStrategy(str, Enum):
    """Available routing strategies for call assignment."""
    FIFO = "fifo"  # First-In-First-Out


class QueueStatus(str, Enum):
    """Status of calls in the queue."""
    QUEUED = "queued"
    ASSIGNED = "assigned"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"


class ParticipantRole(str, Enum):
    """Participant roles for routing."""
    OPERATOR = "operator"
    TRANSLATOR = "translator"


class ParticipantStatus(str, Enum):
    """Participant status values."""
    AVAILABLE = "available"
    BUSY = "busy"
    OFFLINE = "offline"


class Participant(BaseModel):
    """Represents a participant from the presence service."""
    id: str
    role: ParticipantRole
    status: ParticipantStatus


class QueuedCall(BaseModel):
    """Represents a call in the queue."""
    
    call_id: UUID
    priority: CallPriority = CallPriority.NORMAL
    required_role: ParticipantRole = ParticipantRole.OPERATOR
    required_skills: list[str] = Field(default_factory=list)
    language_pair: Optional[tuple[str, str]] = None  # (source_lang, target_lang)
    queued_at: datetime = Field(default_factory=datetime.utcnow)
    status: QueueStatus = QueueStatus.QUEUED
    assigned_participant_id: Optional[str] = None
    assigned_at: Optional[datetime] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v)
        }


class CallAssignment(BaseModel):
    """Represents a call assignment to a participant."""
    
    call_id: UUID
    participant_id: str
    assigned_at: datetime = Field(default_factory=datetime.utcnow)
    routing_strategy: RoutingStrategy
    assignment_score: Optional[float] = None  # For skills-based routing
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v)
        }


class QueueStats(BaseModel):
    """Represents the current status of the queue."""

    total_queued: int
    queued_by_priority: dict[CallPriority, int]
    queued_by_role: dict[ParticipantRole, int]
    average_wait_time_seconds: float
    longest_wait_time_seconds: float
    available_operators: int
    available_translators: int


