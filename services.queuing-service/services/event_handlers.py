import asyncio
from datetime import datetime
from typing import Any
from uuid import UUID
import logging

from models import QueuedCall, QueueStatus
from events import CallStartedEvent, CallQueuedEvent, CallAssignedEvent
from context import ApplicationContext

logger = logging.getLogger(__name__)


class EventHandlers:
    """
    Handles incoming Kafka events and manages the queuing workflow.
    
    This class processes call.started events and manages the complete
    workflow from queuing to assignment.
    """
    
    def __init__(self, context: ApplicationContext):
        """
        Initialize the event handlers.
        
        Args:
            context: Application context with shared resources
        """
        self.context = context
        self._assignment_tasks: dict[UUID, asyncio.Task] = {}
    
    async def handle_call_started(self, event_data: dict[str, Any]) -> None:
        """
        Handle call.started events from Kafka.
        
        This method processes incoming call.started events, adds calls to the queue,
        publishes call.queued events, and initiates the assignment process.
        
        Args:
            event_data: Raw event data from Kafka
        """
        try:
            # Parse the event
            event = CallStartedEvent(**event_data)
            logger.info(f"Processing call.started event for call {event.call_id}")
            
            # Create a queued call
            queued_call = QueuedCall(
                call_id=event.call_id,
                priority=event.priority,
                required_role=event.required_role,
                required_skills=event.required_skills,
                language_pair=event.language_pair,
                metadata=event.metadata
            )
            
            # Add to queue
            success = await self.context.queue_manager.add_call(queued_call)
            if not success:
                logger.error(f"Failed to add call {event.call_id} to queue - queue is full")
                return
            
            # Get queue position for the event
            queue_position = await self.context.queue_manager.get_queue_position(event.call_id)
            if queue_position is None:
                queue_position = 1  # Fallback
            
            # Publish call.queued event
            queued_event = CallQueuedEvent(
                call_id=event.call_id,
                queue_position=queue_position,
                estimated_wait_time_seconds=await self._estimate_wait_time(queued_call)
            )
            
            await self.context.event_producer.publish_event(
                topic="queuing-service.call-queued",
                event=queued_event.model_dump()
            )
            
            logger.info(f"Published call.queued event for call {event.call_id} at position {queue_position}")
            
            # Start assignment process
            assignment_task = asyncio.create_task(
                self._process_assignment(queued_call)
            )
            self._assignment_tasks[event.call_id] = assignment_task
            
        except Exception as e:
            logger.error(f"Error handling call.started event: {e}")
    
    async def _process_assignment(self, call: QueuedCall) -> None:
        """
        Process call assignment in the background.
        
        This method continuously tries to assign a call to an available
        participant until successful or the call times out.
        
        Args:
            call: The call to assign
        """
        try:
            logger.info(f"Starting assignment process for call {call.call_id}")
            
            assignment_attempts = 0
            max_attempts = 60  # Try for up to 5 minutes (5 second intervals)

            while assignment_attempts < max_attempts:
                try:
                    # Try to find an assignment using FIFO strategy
                    assignment = await self.context.routing_engine.find_assignment(call)
                    
                    if assignment:
                        # Remove call from queue
                        removed_call = await self.context.queue_manager.remove_call(
                            call.call_id,
                            status=QueueStatus.ASSIGNED
                        )
                        
                        if removed_call:
                            # Calculate wait time
                            wait_time = int((datetime.utcnow() - call.queued_at).total_seconds())
                            
                            # Publish call.assigned event
                            assigned_event = CallAssignedEvent(
                                call_id=call.call_id,
                                participant_id=assignment.participant_id,
                                routing_strategy=assignment.routing_strategy,
                                wait_time_seconds=wait_time
                            )
                            
                            await self.context.event_producer.publish_event(
                                topic="queuing-service.call-assigned",
                                event=assigned_event.model_dump()
                            )
                            
                            logger.info(
                                f"Successfully assigned call {call.call_id} to participant "
                                f"{assignment.participant_id} using FIFO strategy "
                                f"(wait time: {wait_time}s)"
                            )
                            
                            # Clean up task reference
                            self._assignment_tasks.pop(call.call_id, None)
                            return
                        else:
                            logger.warning(f"Call {call.call_id} was removed from queue before assignment")
                            self._assignment_tasks.pop(call.call_id, None)
                            return
                    
                    # No assignment found, wait and retry
                    assignment_attempts += 1
                    await asyncio.sleep(5)  # Wait 5 seconds before retrying
                    
                except asyncio.CancelledError:
                    logger.info(f"Assignment process cancelled for call {call.call_id}")
                    self._assignment_tasks.pop(call.call_id, None)
                    return
                except Exception as e:
                    logger.error(f"Error in assignment attempt for call {call.call_id}: {e}")
                    assignment_attempts += 1
                    await asyncio.sleep(5)
            
            # Max attempts reached
            logger.warning(f"Max assignment attempts reached for call {call.call_id}")
            await self.context.queue_manager.remove_call(
                call.call_id,
                status=QueueStatus.TIMEOUT
            )
            self._assignment_tasks.pop(call.call_id, None)
            
        except Exception as e:
            logger.error(f"Error in assignment process for call {call.call_id}: {e}")
            self._assignment_tasks.pop(call.call_id, None)
    

    
    async def _estimate_wait_time(self, call: QueuedCall) -> int:
        """
        Estimate wait time for a call based on queue position and available participants.
        
        Args:
            call: The call to estimate wait time for
            
        Returns:
            Estimated wait time in seconds
        """
        try:
            # Get queue position
            position = await self.context.queue_manager.get_queue_position(call.call_id)
            if position is None:
                return 0
            
            # Get available participants count
            participant_counts = await self.context.routing_engine.get_available_participants_count()
            available_count = participant_counts.get(call.required_role, 0)
            
            if available_count == 0:
                return 300  # 5 minutes default if no participants available
            
            # Simple estimation: assume 2 minutes average call duration
            average_call_duration = 120  # seconds
            estimated_wait = (position - 1) * (average_call_duration / available_count)
            
            return max(0, int(estimated_wait))
            
        except Exception as e:
            logger.error(f"Error estimating wait time for call {call.call_id}: {e}")
            return 60  # 1 minute default
    
    async def cleanup(self) -> None:
        """Clean up running assignment tasks."""
        for task in self._assignment_tasks.values():
            if not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass

        self._assignment_tasks.clear()
        logger.info("Event handlers cleanup completed")
