import asyncio
from datetime import datetime, timedelta
from uuid import UUID
import logging

from models import (
    QueuedCall, CallPriority, ParticipantRole, QueueStatus, QueueStats
)

logger = logging.getLogger(__name__)


class QueueManager:
    """
    Manages the call queue state and operations.
    
    This class handles adding calls to the queue, removing them,
    and providing queue statistics and status information.
    """
    
    def __init__(self, max_size: int = 1000, timeout_seconds: int = 300):
        """
        Initialize the queue manager.
        
        Args:
            max_size: Maximum number of calls that can be queued
            timeout_seconds: Timeout for calls in the queue
        """
        self.max_size = max_size
        self.timeout_seconds = timeout_seconds
        
        # Main queue storage - using dict for O(1) lookups
        self._queue: dict[UUID, QueuedCall] = {}
        
        # Priority queues for efficient retrieval
        self._priority_queues: dict[CallPriority, list[UUID]] = {
            priority: [] for priority in CallPriority
        }
        
        # Role-based queues
        self._role_queues: dict[ParticipantRole, list[UUID]] = {
            role: [] for role in ParticipantRole
        }
        
        # Lock for thread-safe operations
        self._lock = asyncio.Lock()
        
        # Background task for cleanup
        self._cleanup_task: asyncio.Task | None = None
        self._running = False
    
    async def start(self) -> None:
        """Start the queue manager background tasks."""
        self._running = True
        self._cleanup_task = asyncio.create_task(self._cleanup_expired_calls())
        logger.info("Queue manager started")
    
    async def stop(self) -> None:
        """Stop the queue manager background tasks."""
        self._running = False
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        logger.info("Queue manager stopped")
    
    async def cleanup(self) -> None:
        """Cleanup resources."""
        await self.stop()
    
    async def add_call(self, call: QueuedCall) -> bool:
        """
        Add a call to the queue.
        
        Args:
            call: The call to add to the queue
            
        Returns:
            True if the call was added successfully, False if queue is full
        """
        async with self._lock:
            if len(self._queue) >= self.max_size:
                logger.warning(f"Queue is full, cannot add call {call.call_id}")
                return False
            
            # Add to main queue
            self._queue[call.call_id] = call
            
            # Add to priority queue
            self._priority_queues[call.priority].append(call.call_id)
            
            # Add to role queue
            self._role_queues[call.required_role].append(call.call_id)
            
            logger.info(f"Added call {call.call_id} to queue (priority: {call.priority}, role: {call.required_role})")
            return True
    
    async def remove_call(self, call_id: UUID, status: QueueStatus = QueueStatus.ASSIGNED) -> QueuedCall | None:
        """
        Remove a call from the queue.
        
        Args:
            call_id: The ID of the call to remove
            status: The final status of the call
            
        Returns:
            The removed call if found, None otherwise
        """
        async with self._lock:
            call = self._queue.get(call_id)
            if not call:
                return None
            
            # Update call status
            call.status = status
            if status == QueueStatus.ASSIGNED:
                call.assigned_at = datetime.utcnow()
            
            # Remove from main queue
            del self._queue[call_id]
            
            # Remove from priority queue
            try:
                self._priority_queues[call.priority].remove(call_id)
            except ValueError:
                pass
            
            # Remove from role queue
            try:
                self._role_queues[call.required_role].remove(call_id)
            except ValueError:
                pass
            
            logger.info(f"Removed call {call_id} from queue (status: {status})")
            return call
    
    async def get_next_call(
        self, 
        role: ParticipantRole,
        priority_order: list[CallPriority] | None = None
    ) -> QueuedCall | None:
        """
        Get the next call for assignment based on role and priority.
        
        Args:
            role: The required participant role
            priority_order: Order of priorities to check (defaults to high->normal->low->critical)
            
        Returns:
            The next call to assign, or None if no suitable calls
        """
        if priority_order is None:
            priority_order = [CallPriority.CRITICAL, CallPriority.HIGH, CallPriority.NORMAL, CallPriority.LOW]
        
        async with self._lock:
            # Check each priority level in order
            for priority in priority_order:
                # Get calls with this priority and role
                role_calls = self._role_queues[role]
                priority_calls = self._priority_queues[priority]
                
                # Find intersection (calls that match both role and priority)
                matching_calls = [call_id for call_id in role_calls if call_id in priority_calls]
                
                if matching_calls:
                    # Return the oldest call (FIFO within priority)
                    call_id = matching_calls[0]
                    return self._queue[call_id]
            
            return None
    
    async def get_queue_status(self, available_operators: int = 0, available_translators: int = 0) -> QueueStats:
        """
        Get current queue status and statistics.
        
        Args:
            available_operators: Number of available operators
            available_translators: Number of available translators
            
        Returns:
            Queue status response with statistics
        """
        async with self._lock:
            total_queued = len(self._queue)
            
            # Count by priority
            queued_by_priority = {
                priority: len(call_ids) for priority, call_ids in self._priority_queues.items()
            }
            
            # Count by role
            queued_by_role = {
                role: len(call_ids) for role, call_ids in self._role_queues.items()
            }
            
            # Calculate wait times
            now = datetime.utcnow()
            wait_times = [
                (now - call.queued_at).total_seconds() 
                for call in self._queue.values()
            ]
            
            average_wait_time = sum(wait_times) / len(wait_times) if wait_times else 0.0
            longest_wait_time = max(wait_times) if wait_times else 0.0
            
            return QueueStats(
                total_queued=total_queued,
                queued_by_priority=queued_by_priority,
                queued_by_role=queued_by_role,
                average_wait_time_seconds=average_wait_time,
                longest_wait_time_seconds=longest_wait_time,
                available_operators=available_operators,
                available_translators=available_translators
            )
    
    async def get_queue_position(self, call_id: UUID) -> int | None:
        """
        Get the position of a call in the queue.
        
        Args:
            call_id: The call ID to check
            
        Returns:
            Queue position (1-based) or None if call not found
        """
        async with self._lock:
            call = self._queue.get(call_id)
            if not call:
                return None
            
            # Count calls ahead of this one with same or higher priority
            position = 1
            for other_call in self._queue.values():
                if (other_call.call_id != call_id and 
                    other_call.required_role == call.required_role and
                    other_call.queued_at < call.queued_at):
                    # Check if other call has higher or equal priority
                    priority_order = [CallPriority.CRITICAL, CallPriority.HIGH, CallPriority.NORMAL, CallPriority.LOW]
                    if priority_order.index(other_call.priority) <= priority_order.index(call.priority):
                        position += 1
            
            return position
    
    async def _cleanup_expired_calls(self) -> None:
        """Background task to clean up expired calls."""
        while self._running:
            try:
                await asyncio.sleep(30)  # Check every 30 seconds
                
                now = datetime.utcnow()
                timeout_threshold = now - timedelta(seconds=self.timeout_seconds)
                
                expired_calls = []
                async with self._lock:
                    for call in self._queue.values():
                        if call.queued_at < timeout_threshold:
                            expired_calls.append(call.call_id)
                
                # Remove expired calls
                for call_id in expired_calls:
                    await self.remove_call(call_id, QueueStatus.TIMEOUT)
                    logger.info(f"Removed expired call {call_id} from queue")
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup task: {e}")
                await asyncio.sleep(5)  # Wait before retrying
