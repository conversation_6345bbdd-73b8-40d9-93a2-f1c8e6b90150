import httpx

from models import ParticipantRole, Participant


class ParticipantPresenceClient:
    """
    Client for the Participant Presence Service.
    
    This client handles communication with the participant presence service
    to fetch available operators and translators.
    """
    
    def __init__(self, base_url: str, timeout: float = 10.0):
        """
        Initialize the participant presence client.
        
        Args:
            base_url: Base URL of the participant presence service
            timeout: Request timeout in seconds
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self._client: httpx.AsyncClient | None = None
    
    async def _get_client(self) -> httpx.AsyncClient:
        """Get or create the HTTP client."""
        if self._client is None:
            self._client = httpx.AsyncClient(
                timeout=httpx.Timeout(self.timeout),
                limits=httpx.Limits(max_connections=10, max_keepalive_connections=5)
            )
        return self._client
    
    async def close(self) -> None:
        """Close the HTTP client."""
        if self._client:
            await self._client.aclose()
            self._client = None
    
    async def get_available_participants(
        self,
        role: ParticipantRole | None = None,
        status: str | None = "available"
    ) -> list[Participant]:
        """
        Get available participants from the presence service.

        Args:
            role: Filter by participant role (operator/translator)
            status: Filter by participant status (default: "available")

        Returns:
            List of Participant objects

        Raises:
            httpx.HTTPError: If the request fails
        """
        client = await self._get_client()

        params = {}
        if status:
            params["status"] = status
        if role:
            params["role"] = role.value

        try:
            response = await client.get(
                f"{self.base_url}/internal/participants",
                params=params
            )
            response.raise_for_status()
            participants_data = response.json()

            # Convert response data to Participant objects
            participants = []
            for participant_data in participants_data:
                try:
                    participant = Participant(**participant_data)
                    participants.append(participant)
                except Exception as e:
                    # Log invalid participant data but continue
                    print(f"Warning: Invalid participant data {participant_data}: {e}")

            return participants
        except httpx.HTTPError as e:
            raise httpx.HTTPError(f"Failed to fetch participants: {e}")
    
    async def get_available_operators(self) -> list[Participant]:
        """
        Get available operators.

        Returns:
            List of operator Participant objects
        """
        return await self.get_available_participants(role=ParticipantRole.OPERATOR)

    async def get_available_translators(self) -> list[Participant]:
        """
        Get available translators.

        Returns:
            List of translator Participant objects
        """
        return await self.get_available_participants(role=ParticipantRole.TRANSLATOR)

    async def get_available_operator_ids(self) -> list[str]:
        """
        Get available operator IDs only.

        Returns:
            List of operator IDs
        """
        operators = await self.get_available_operators()
        return [op.id for op in operators]

    async def get_available_translator_ids(self) -> list[str]:
        """
        Get available translator IDs only.

        Returns:
            List of translator IDs
        """
        translators = await self.get_available_translators()
        return [trans.id for trans in translators]

    async def get_all_participants(self) -> list[Participant]:
        """
        Get all participants regardless of status.

        Returns:
            List of all Participant objects
        """
        return await self.get_available_participants(status=None)

    async def health_check(self) -> bool:
        """
        Perform a health check on the participant presence service.

        Returns:
            True if the service is healthy, False otherwise
        """
        try:
            client = await self._get_client()
            response = await client.get(f"{self.base_url}/internal/participants")
            return response.status_code == 200
        except Exception:
            return False
