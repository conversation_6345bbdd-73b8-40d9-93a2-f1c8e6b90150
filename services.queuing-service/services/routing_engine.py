"""
Routing engine for implementing call assignment strategies.

This module implements FIFO (First-In-First-Out) routing strategy
for assigning calls to available participants.
"""

import logging

from models import (
    QueuedCall, CallAssignment, RoutingStrategy,
    ParticipantRole
)
from services.participant_presence_client import ParticipantPresenceClient

logger = logging.getLogger(__name__)


class RoutingEngine:
    """
    Implements FIFO routing strategy for call assignment.

    This class handles the logic for assigning calls to available
    participants using First-In-First-Out (FIFO) strategy.
    """

    def __init__(self, participant_client: ParticipantPresenceClient):
        """
        Initialize the routing engine.

        Args:
            participant_client: Client for fetching available participants
        """
        self.participant_client = participant_client
    
    async def find_assignment(self, call: QueuedCall) -> CallAssignment | None:
        """
        Find a participant assignment for a call using FIFO strategy.

        Args:
            call: The call to assign

        Returns:
            CallAssignment if a suitable participant is found, None otherwise
        """
        try:
            return await self._fifo_routing(call)
        except Exception as e:
            logger.error(f"Error in FIFO routing for call {call.call_id}: {e}")
            return None
    
    async def _fifo_routing(self, call: QueuedCall) -> CallAssignment | None:
        """
        First-In-First-Out routing strategy.

        Simply assigns the call to any available participant with the required role.

        Args:
            call: The call to assign

        Returns:
            CallAssignment if successful, None otherwise
        """
        try:
            # Get available participants for the required role
            if call.required_role == ParticipantRole.OPERATOR:
                participants = await self.participant_client.get_available_operators()
            else:
                participants = await self.participant_client.get_available_translators()

            if not participants:
                logger.debug(f"No available {call.required_role.value}s for call {call.call_id}")
                return None

            # Select the first available participant (FIFO)
            selected_participant = participants[0]

            return CallAssignment(
                call_id=call.call_id,
                participant_id=selected_participant.id,
                routing_strategy=RoutingStrategy.FIFO
            )

        except Exception as e:
            logger.error(f"Error in FIFO routing for call {call.call_id}: {e}")
            return None
    
    async def get_available_participants_count(self) -> dict[ParticipantRole, int]:
        """
        Get the count of available participants by role.

        Returns:
            Dictionary with participant counts by role
        """
        try:
            operators = await self.participant_client.get_available_operators()
            translators = await self.participant_client.get_available_translators()

            return {
                ParticipantRole.OPERATOR: len(operators),
                ParticipantRole.TRANSLATOR: len(translators)
            }

        except Exception as e:
            logger.error(f"Error getting participant counts: {e}")
            return {
                ParticipantRole.OPERATOR: 0,
                ParticipantRole.TRANSLATOR: 0
            }
