from datetime import datetime
from typing import Optional, Dict, Any
from uuid import UUID
from pydantic import BaseModel, Field

from models import CallPriority, ParticipantRole, RoutingStrategy


class CallStartedEvent(BaseModel):
    """Event model for call.started events from Kafka."""
    
    event_id: UUID
    event_type: str = "call.started"
    timestamp: datetime
    source_service: str
    call_id: UUID
    priority: CallPriority = CallPriority.NORMAL
    required_role: ParticipantRole = ParticipantRole.OPERATOR
    required_skills: list[str] = Field(default_factory=list)
    language_pair: Optional[tuple[str, str]] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v)
        }


class CallQueuedEvent(BaseModel):
    """Event model for call.queued events to publish to Kafka."""
    
    event_id: UUID = Field(default_factory=lambda: UUID(hex=__import__('uuid').uuid4().hex))
    event_type: str = "call.queued"
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    source_service: str = "queuing-service"
    call_id: UUID
    queue_position: int
    estimated_wait_time_seconds: Optional[int] = None
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v)
        }


class CallAssignedEvent(BaseModel):
    """Event model for call.assigned events to publish to Kafka."""
    
    event_id: UUID = Field(default_factory=lambda: UUID(hex=__import__('uuid').uuid4().hex))
    event_type: str = "call.assigned"
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    source_service: str = "queuing-service"
    call_id: UUID
    participant_id: str
    routing_strategy: RoutingStrategy
    wait_time_seconds: int
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v)
        }
