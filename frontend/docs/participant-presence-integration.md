# Participant Presence Service Integration

This document describes the frontend integration with the participant-presence service, which tracks the availability of operators and translators in real-time.

## Overview

The integration provides:
- Real-time WebSocket connection to track participant status
- REST API integration for fetching available participants
- React components for displaying participant presence
- Custom hooks for managing presence state
- Authentication using Supabase JWT tokens

## Architecture

### Service Layer (`lib/services/participant-presence.ts`)

The `ParticipantPresenceService` class handles:
- WebSocket connections with automatic reconnection
- REST API calls to fetch available participants
- Status updates (AVAILABLE, BUSY, OFFLINE)
- Event listeners for real-time updates
- Authentication token management

### Hooks (`lib/hooks/use-participant-presence.ts`)

Three main hooks are provided:

1. **`useParticipantPresence`** - Full-featured hook with WebSocket and REST API integration
2. **`useAvailableParticipants`** - Simplified hook for fetching participant lists
3. **`useParticipantStatus`** - Hook for managing individual participant status

### Components

1. **`ParticipantPresence`** - Main component displaying available operators and translators
2. **`OperatorPresenceWidget`** - Specialized widget for operator status management
3. **`OperatorDashboard`** - Complete dashboard integrating presence functionality

## Configuration

Add the following environment variable to your `.env` file:

```env
NEXT_PUBLIC_PARTICIPANT_PRESENCE_URL=http://localhost:8000
```

## Usage Examples

### Basic Component Usage

```tsx
import { ParticipantPresence } from '@/components/participant-presence'

function MyComponent() {
  return (
    <ParticipantPresence 
      showOperators={true}
      showTranslators={true}
      showControls={true}
      autoConnect={true}
    />
  )
}
```

### Using Hooks

```tsx
import { useParticipantPresence, ParticipantStatus } from '@/lib/hooks/use-participant-presence'

function MyComponent() {
  const [state, actions] = useParticipantPresence({
    autoConnect: true,
    refreshInterval: 30000
  })

  return (
    <div>
      <p>Status: {state.connectionStatus}</p>
      <p>Operators: {state.operators.length}</p>
      <button onClick={() => actions.updateStatus(ParticipantStatus.BUSY)}>
        Set Busy
      </button>
    </div>
  )
}
```

### Simplified Hooks

```tsx
import { useAvailableParticipants, useParticipantStatus } from '@/lib/hooks/use-participant-presence'

function TeamStatus() {
  const { operators, translators, refresh } = useAvailableParticipants('both')
  
  return (
    <div>
      <p>Available Operators: {operators.length}</p>
      <p>Available Translators: {translators.length}</p>
      <button onClick={refresh}>Refresh</button>
    </div>
  )
}

function StatusControl() {
  const { status, updateStatus } = useParticipantStatus()
  
  return (
    <div>
      <p>Current Status: {status}</p>
      <button onClick={() => updateStatus(ParticipantStatus.AVAILABLE)}>
        Set Available
      </button>
    </div>
  )
}
```

## API Reference

### ParticipantPresenceService

#### Methods

- `getAvailableOperators()`: Promise<string[]> - Fetch available operators
- `getAvailableTranslators()`: Promise<string[]> - Fetch available translators
- `connectWebSocket()`: Promise<void> - Connect to WebSocket
- `updateStatus(status)`: void - Update participant status
- `disconnect()`: void - Disconnect WebSocket
- `addParticipantListener(callback)`: void - Add participant update listener
- `addStatusListener(callback)`: void - Add connection status listener

#### Events

- Connection status changes: 'connected' | 'disconnected' | 'error'
- Participant list updates (when implemented on backend)

### Component Props

#### ParticipantPresence

```tsx
interface ParticipantPresenceProps {
  showOperators?: boolean      // Show operators section (default: true)
  showTranslators?: boolean    // Show translators section (default: true)
  showControls?: boolean       // Show status controls (default: false)
  autoConnect?: boolean        // Auto-connect WebSocket (default: true)
}
```

#### OperatorPresenceWidget

```tsx
interface OperatorPresenceWidgetProps {
  operatorId?: string          // Operator ID for display
  showStatusControls?: boolean // Show status controls (default: true)
  compact?: boolean           // Compact layout (default: false)
  initialStatus?: ParticipantStatus // Initial status (default: AVAILABLE)
}
```

## Authentication

The service automatically handles authentication using Supabase JWT tokens:

1. Retrieves the current session token from Supabase
2. Includes the token in WebSocket connection query parameters
3. Backend validates the token using Supabase JWKs
4. Connection is established only after successful validation

## Error Handling

The integration includes comprehensive error handling:

- WebSocket connection failures with automatic reconnection
- REST API errors with user-friendly messages
- Authentication failures
- Network connectivity issues

## Real-time Updates

The WebSocket connection provides real-time updates for:

- Participant status changes (AVAILABLE ↔ BUSY ↔ OFFLINE)
- Connection/disconnection events
- Service availability

## Testing

A demo page is available at `/participant-presence-demo` that showcases:

- Basic component usage
- Hook implementations
- Real-time status updates
- Error handling scenarios

## Performance Considerations

- WebSocket connections are managed per component instance
- Automatic cleanup on component unmount
- Configurable refresh intervals for REST API calls
- Efficient state management with minimal re-renders

## Future Enhancements

Potential improvements:
- Participant list real-time updates via WebSocket
- Presence history tracking
- Advanced filtering and search
- Integration with call routing system
- Presence analytics and reporting
