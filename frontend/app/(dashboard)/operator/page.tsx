import { redirect } from 'next/navigation'
import { createClient } from '@/lib/supabase/server'
import { OperatorDashboard } from '@/components/Operator/OperatorDashboard'

export default async function OperatorPage() {
  const supabase = await createClient()

  const { data, error } = await supabase.auth.getClaims()
  if (error || !data?.claims) {
    redirect('/auth/login')
  }

  return <OperatorDashboard />
}
