import { redirect } from 'next/navigation'

import { <PERSON>goutButton } from "@/components/logout-button";
import { createClient } from '@/lib/supabase/server'
import { <PERSON><PERSON> } from "@/components/ui/button";

export default async function Home() {
  const supabase = await createClient()
  
  const { data, error } = await supabase.auth.getClaims()
    if (error || !data?.claims) {
      redirect('/auth/login')
  }
  
  return (
    <div className="flex flex-col h-svh w-full items-center justify-center gap-2">
      <p>
        Hello <span>{data.claims.email}</span>
      </p>

      <div className="flex p-3 gap-3">
        <a href="/manager"><Button><PERSON>gin as Manager</Button></a>
        <a href="/operator"><Button>Login as Operator</Button></a>
        <a href="/translator"><Button>Login as Translator</Button></a>
      </div>

      <LogoutButton />
    </div>
  )
}
