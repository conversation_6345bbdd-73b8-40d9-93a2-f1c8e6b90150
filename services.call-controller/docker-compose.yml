version: '3.8'

services:
  call-controller-ts:
    build:
      context: .
      target: development
    ports:
      - "8002:8002"
    environment:
      - NODE_ENV=development
      - DEBUG=true
      - HOST=0.0.0.0
      - PORT=8002
      - DATABASE_URL=****************************************/cortexa
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - VOICE_ROUTER_URL=http://voice-router:3000
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      - postgres
      - kafka
    networks:
      - cortexa-network

  postgres:
    image: postgres:15.4-alpine
    environment:
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=cortexa
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - cortexa-network

  kafka:
    image: confluentinc/cp-kafka:latest
    environment:
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
    ports:
      - "9092:9092"
    depends_on:
      - zookeeper
    networks:
      - cortexa-network

  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - cortexa-network

volumes:
  postgres_data:

networks:
  cortexa-network:
    external: true
