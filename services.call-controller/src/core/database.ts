import { PrismaClient } from '@prisma/client';
import { config } from '@/config';
import { logger } from '@/core/logger';

// Create Prisma client instance
export const prisma = new PrismaClient({
  log: config.debug ? ['query', 'info', 'warn', 'error'] : ['warn', 'error'],
  datasources: {
    db: {
      url: config.databaseUrl,
    },
  },
});

/**
 * Test database connection to ensure it's working properly.
 * Follows the fail early principle by testing connectivity during startup.
 * 
 * @throws {Error} If database connection fails
 */
export async function testDatabaseConnection(): Promise<void> {
  logger.info('Testing database connection...');

  try {
    // Execute a simple query to test connectivity
    const result = await prisma.$queryRaw<Array<{ test: number }>>`SELECT 1 as test`;
    
    if (!result || result.length === 0 || result[0]?.test !== 1) {
      throw new Error('Database connection test failed: unexpected result');
    }

    logger.info('Database connection test successful');
  } catch (error) {
    logger.error('Database connection test failed:', error);
    throw new Error(`Failed to connect to database: ${error}`);
  }
}

/**
 * Gracefully disconnect from the database
 */
export async function disconnectDatabase(): Promise<void> {
  try {
    await prisma.$disconnect();
    logger.info('Database disconnected successfully');
  } catch (error) {
    logger.error('Error disconnecting from database:', error);
    throw error;
  }
}
