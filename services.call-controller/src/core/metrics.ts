import { Request, Response, NextFunction } from 'express';
import { register, collectDefaultMetrics, Counter, Histogram, Gauge } from 'prom-client';
import { getLogger } from '@/core/logger';

const logger = getLogger('Metrics');

// Collect default metrics (CPU, memory, etc.)
collectDefaultMetrics();

// Custom metrics
export const httpRequestsTotal = new Counter({
  name: 'http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code'],
});

export const httpRequestDuration = new Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10],
});

export const callsTotal = new Counter({
  name: 'calls_total',
  help: 'Total number of calls created',
  labelNames: ['status'],
});

export const callsActive = new Gauge({
  name: 'calls_active',
  help: 'Number of currently active calls',
});

export const callDuration = new Histogram({
  name: 'call_duration_seconds',
  help: 'Duration of calls in seconds',
  buckets: [30, 60, 120, 300, 600, 1200, 1800, 3600],
});

export const voiceRouterRequests = new Counter({
  name: 'voice_router_requests_total',
  help: 'Total number of requests to voice router service',
  labelNames: ['method', 'status'],
});

export const kafkaEventsPublished = new Counter({
  name: 'kafka_events_published_total',
  help: 'Total number of events published to Kafka',
  labelNames: ['topic', 'event_type'],
});

/**
 * Middleware to collect HTTP metrics
 */
export function metricsMiddleware(req: Request, res: Response, next: NextFunction): void {
  const start = Date.now();

  // Override res.end to capture metrics when response is sent
  const originalEnd = res.end;
  res.end = function(chunk?: any, encoding?: any): Response {
    const duration = (Date.now() - start) / 1000;
    const route = req.route?.path || req.path;
    const method = req.method;
    const statusCode = res.statusCode.toString();

    // Record metrics
    httpRequestsTotal.inc({ method, route, status_code: statusCode });
    httpRequestDuration.observe({ method, route, status_code: statusCode }, duration);

    // Call original end method
    return originalEnd.call(this, chunk, encoding);
  };

  next();
}

/**
 * Metrics endpoint handler
 */
export async function metricsHandler(req: Request, res: Response): Promise<void> {
  try {
    res.set('Content-Type', register.contentType);
    const metrics = await register.metrics();
    res.end(metrics);
  } catch (error) {
    logger.error('Failed to generate metrics:', error);
    res.status(500).end('Failed to generate metrics');
  }
}

/**
 * Setup metrics collection for the application
 */
export function setupMetrics(): void {
  logger.info('Metrics collection initialized');
}

// Helper functions to update custom metrics
export function incrementCallsTotal(status: string): void {
  callsTotal.inc({ status });
}

export function setCallsActive(count: number): void {
  callsActive.set(count);
}

export function observeCallDuration(durationSeconds: number): void {
  callDuration.observe(durationSeconds);
}

export function incrementVoiceRouterRequests(method: string, status: string): void {
  voiceRouterRequests.inc({ method, status });
}

export function incrementKafkaEventsPublished(topic: string, eventType: string): void {
  kafkaEventsPublished.inc({ topic, event_type: eventType });
}
