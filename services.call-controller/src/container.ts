import { container, Lifecycle } from 'tsyringe';
import { prisma } from '@/core/database';
import { getLogger } from '@/core/logger';
import { INJECTION_TOKENS } from '@/utils/injection-tokens';
import { CallRepository } from '@/features/call/call.repository';
import { CallService } from '@/features/call/call.service';
import { CallController } from '@/features/call/call.controller';
import { VoiceRouterService } from '@/features/voice-router/voice-router.service';
import { EventPublisher } from '@/features/messaging/messaging.publisher';

// --- CONTAINER REGISTRATION ---

// Register primitive values and external clients
container.register(INJECTION_TOKENS.PrismaClient, { useValue: prisma });
container.register(INJECTION_TOKENS.Logger, { useValue: getLogger('App') });

// Register repositories as singletons (one instance for the whole app)
container.register<CallRepository>(CallRepository, {
  useClass: CallRepository,
}, { lifecycle: Lifecycle.Singleton });

// Register services as singletons
container.register<VoiceRouterService>(VoiceRouterService, {
  useClass: VoiceRouterService,
}, { lifecycle: Lifecycle.Singleton });

container.register<EventPublisher>(EventPublisher, {
  useClass: EventPublisher,
}, { lifecycle: Lifecycle.Singleton });

container.register<CallService>(CallService, {
  useClass: CallService,
}, { lifecycle: Lifecycle.Singleton });

container.register<CallController>(CallController, {
  useClass: CallController,
}, { lifecycle: Lifecycle.Singleton });

export default container;