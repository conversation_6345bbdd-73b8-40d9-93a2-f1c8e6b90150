import 'reflect-metadata';
import app from '@/app';
import { config } from '@/config';
import { disconnectDatabase } from '@/core/database';
import { getLogger } from '@/core/logger';
import container from '@/container';
import { EventPublisher } from '@/features/messaging/messaging.publisher';

const appLogger = getLogger('Main');

/**
 * Main application startup function.
 */
async function main(): Promise<void> {
  appLogger.info('Starting Call Control Service', {
    service: config.serviceName,
    port: config.port,
    environment: config.nodeEnv,
  });

  let server: any = null;

  try {
    // --- DI-based Initialization ---
    // Instead of initializing a context, we initialize services resolved from the container.
    const eventPublisher = container.resolve(EventPublisher);
    await eventPublisher.initialize();

    appLogger.info('Application dependencies initialized successfully');

    // Start HTTP server
    server = app.listen(config.port, config.host, () => {
      appLogger.info(`Call Control service started successfully`, {
        host: config.host,
        port: config.port,
        url: `http://${config.host}:${config.port}`,
      });
    });

    // --- Graceful Shutdown ---
    const gracefulShutdown = async (signal: string) => {
      appLogger.info(`Received ${signal}, starting graceful shutdown...`);

      if (server) {
        server.close(async () => {
          appLogger.info('HTTP server closed');
          try {
            // Clean up resources using services resolved from the container
            const eventPublisher = container.resolve(EventPublisher);
            await eventPublisher.cleanup();

            await disconnectDatabase();

            appLogger.info('Call Control service shutdown complete');
            process.exit(0);
          } catch (error) {
            appLogger.error('Error during graceful shutdown:', error);
            process.exit(1);
          }
        });
      } else {
        process.exit(0);
      }

      setTimeout(() => {
        appLogger.error('Graceful shutdown timed out, forcing exit');
        process.exit(1);
      }, 30000);
    };

    // Handle shutdown signals
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    process.on('uncaughtException', (error) => {
      appLogger.error('Uncaught exception:', error);
      gracefulShutdown('uncaughtException');
    });
    process.on('unhandledRejection', (reason, promise) => {
      appLogger.error('Unhandled rejection at:', promise, 'reason:', reason);
      gracefulShutdown('unhandledRejection');
    });

  } catch (error) {
    appLogger.error('Fatal error during startup:', error);
    if (server) server.close();
    await disconnectDatabase().catch(dbError => appLogger.error('DB disconnect failed on startup error:', dbError));
    process.exit(1);
  }
}

// Start the application
main();