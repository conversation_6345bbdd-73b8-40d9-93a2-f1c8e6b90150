import { <PERSON><PERSON><PERSON>, Producer, Partitioners } from 'kafkajs';
import { injectable } from 'tsyringe';
import { config } from '@/config';
import { getLogger } from '@/core/logger';
import { BaseEvent } from '@/features/messaging/messaging.schema';

const logger = getLogger('EventPublisher');

/**
 * Enhanced event publisher for call-controller events.
 * Handles publishing events to Kafka topics.
 */
@injectable()
export class EventPublisher {
  private kafka: Kafka | null = null;
  private producer: Producer | null = null;

  constructor() {
    this.kafka = new Kafka({
      clientId: config.serviceName,
      brokers: config.kafkaBootstrapServers.split(','),
    });
  }

  /**
   * Initialize Kafka producer.
   */
  async initialize(): Promise<void> {
    if (!this.kafka) {
      throw new Error('Kafka client not initialized');
    }

    try {
      this.producer = this.kafka.producer({
        createPartitioner: Partitioners.LegacyPartitioner,
      });

      await this.producer.connect();
      logger.info('Event publisher initialized and connected to Kafka');
    } catch (error) {
      logger.error('Failed to initialize event publisher:', error);
      throw error;
    }
  }

  /**
   * Cleanup the event publisher.
   */
  async cleanup(): Promise<void> {
    if (!this.producer) {
      return;
    }

    try {
      await this.producer.disconnect();
      this.producer = null;
      logger.info('Event publisher cleaned up');

    } catch (error) {
      logger.error('Error during event publisher cleanup:', error);
    } finally {
      this.producer = null;
    }
  }

  /**
   * Publish an event to Kafka.
   * @param event - The event to publish
   * @param topic - Optional topic override (defaults to event type based topic)
   */
  async publishEvent(event: BaseEvent, topic?: string): Promise<void> {
    if (!this.producer) {
      throw new Error('Event publisher not initialized');
    }

    const targetTopic = topic || `call-control.${event.eventType.replace('.', '-')}`;

    const eventWithSource = {
     ...event,
      sourceService: 'call-controller',
    };

    try {
      const eventData = JSON.stringify({
       ...eventWithSource,
        timestamp: eventWithSource.timestamp.toISOString(),
      });

      await this.producer.send({
        topic: targetTopic,
        messages: [{ value: eventData }],
      });

      logger.debug(`Published event ${event.eventType} to topic ${targetTopic}`, {
        eventId: event.eventId,
        topic: targetTopic,
      });

    } catch (error) {
      logger.error(`Failed to publish event ${event.eventType} to topic ${targetTopic}:`, error);
      throw error;
    }
  }
}