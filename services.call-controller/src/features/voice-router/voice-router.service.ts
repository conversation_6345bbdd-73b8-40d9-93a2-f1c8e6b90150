import axios, { AxiosInstance, InternalAxiosRequestConfig } from 'axios';
import { config } from '@/config';
import { getLogger } from '@/core/logger';


const logger = getLogger('VoiceRouterService');


/**
 * A client service for interacting with the internal API of the voice-router.
 * Provides methods to create and manage media sessions.
 */
export class VoiceRouterService {
  private readonly client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: config.voiceRouterUrl,
      timeout: 10000, // 10 second timeout
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Add request/response interceptors for logging
    this.client.interceptors.request.use(
      (config: InternalAxiosRequestConfig) => {
        logger.debug(`Making request to voice-router: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error: Error) => {
        logger.error('Voice-router request error:', error);
        return Promise.reject(error);
      }
    );

    this.client.interceptors.response.use(
      (response: any) => {
        logger.debug(`Voice-router response: ${response.status} ${response.statusText}`);
        return response;
      },
      (error: any) => {
        logger.error('Voice-router response error:', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * Commands the voice-router to create a new media session.
   * @returns The unique media_session_id
   * @throws {Error} If the request fails
   */
  async createMediaSession(): Promise<string> {
    try {
      logger.info('Creating new media session via voice-router');
      
      const response = await this.client.post<{ mediaSessionId: string }>('/internal/calls');
      
      const mediaSessionId = response.data.mediaSessionId;
      if (!mediaSessionId) {
        throw new Error('Voice-router did not return a media session ID');
      }

      logger.info(`Created media session: ${mediaSessionId}`);
      return mediaSessionId;

    } catch (error) {
      logger.error('Failed to create media session:', error);
      
      if (axios.isAxiosError(error)) {
        const status = error.response?.status;
        const message = error.response?.data?.error || error.message;
        throw new Error(`Voice-router error (${status}): ${message}`);
      }
      
      throw error;
    }
  }

  /**
   * Commands the voice-router to terminate a media session.
   * @param mediaSessionId - The ID of the media session to terminate
   * @throws {Error} If the request fails
   */
  async endMediaSession(mediaSessionId: string): Promise<void> {
    try {
      logger.info(`Ending media session: ${mediaSessionId}`);
      
      await this.client.post(`/internal/calls/${mediaSessionId}/end`);
      
      logger.info(`Successfully ended media session: ${mediaSessionId}`);

    } catch (error) {
      logger.error(`Failed to end media session ${mediaSessionId}:`, error);
      
      if (axios.isAxiosError(error)) {
        const status = error.response?.status;
        const message = error.response?.data?.error || error.message;
        throw new Error(`Voice-router error (${status}): ${message}`);
      }
      
      throw error;
    }
  }
}
