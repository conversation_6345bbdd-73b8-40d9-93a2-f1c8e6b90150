import { z } from 'zod';

/**
 * Schema for validating the request body when creating a call.
 * This is a strict empty object, meaning no body properties are allowed.
 */
export const createCallSchema = z.strictObject({});

/**
 * Schema for validating URL parameters that contain a call ID.
 * Ensures 'id' is present and is a valid UUID string.
 */
export const callParamsSchema = z.object({
  id: z.uuid('A valid Call ID (UUID) is required in the URL'),
});
