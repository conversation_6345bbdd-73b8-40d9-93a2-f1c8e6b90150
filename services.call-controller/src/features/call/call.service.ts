import { injectable } from 'tsyringe';
import { VoiceRouterService } from '@/features/voice-router/voice-router.service';
import { EventPublisher } from '@/features/messaging/messaging.publisher';
import { createCallStartedEvent, createCallEndedEvent } from '@/features/messaging/messaging.schema';
import { getLogger } from '@/core/logger';
import { Call, CallStatus } from '@prisma/client';
import { CallRepository } from './call.repository';



const logger = getLogger('CallService');

/**
 * Contains the business logic for managing calls.
 * Orchestrates call creation, management, and termination.
 */
@injectable()
export class CallService {
  constructor(
    private readonly callRepository: CallRepository,
    private readonly voiceRouterService: VoiceRouterService,
    private readonly eventPublisher: EventPublisher
  ) {}

  /**
   * Orchestrates the creation of a new call, including its media session.
   * @returns The created call
   * @throws {Error} If call creation fails
   */
  async createNewCall(): Promise<Call> {
    logger.info('Creating new call');

    // Command the voice-router to create a media session first
    const mediaSessionId = await this.voiceRouterService.createMediaSession();
    logger.info(`Media session created: ${mediaSessionId}`);

    // If successful, create the call record in our database
    const newCall = await this.callRepository.create({mediaSessionId});

    logger.info(`Call created in database: ${newCall.id}`);

    // Publish call started event
    try {
      const event = createCallStartedEvent(newCall.id);
      await this.eventPublisher.publishEvent(event);
      logger.debug(`Published call started event for call: ${newCall.id}`);
    } catch (eventError) {
      // Log warning but don't fail the call creation
      logger.warn(`Failed to publish call started event for call ${newCall.id}:`, eventError);
    }

    return newCall;
  }

  /**
   * Orchestrates ending a call, terminating the media session and updating status.
   * @param callId - The ID of the call to end
   * @returns The updated call, or null if not found
   * @throws {Error} If call ending fails
   */
  async endCall(callId: string): Promise<Call | null> {
    logger.info(`Ending call: ${callId}`);

    // Get the call from database
    const dbCall = await this.callRepository.findById(callId);
    if (!dbCall || dbCall.status === CallStatus.ENDED) {
      logger.warn(`Call ${callId} not found or already ended`);
      return dbCall;
    }

    // Delete the media session if it exists
    if (dbCall.mediaSessionId) {
      try {
        await this.voiceRouterService.endMediaSession(dbCall.mediaSessionId);
        logger.info(`Media session ended: ${dbCall.mediaSessionId}`);
      } catch (mediaError) {
        // Log warning but continue with call ending
        logger.warn(`Could not end media session ${dbCall.mediaSessionId}:`, mediaError);
      }
    }

    // Calculate call duration
    const callStartTime = dbCall.createdAt;
    const callEndTime = new Date();
    const durationSeconds = (callEndTime.getTime() - callStartTime.getTime()) / 1000;

    // Update call in database
    const updatedCall = await this.callRepository.update(callId, {
      status: CallStatus.ENDED,
      endedAt: callEndTime,
    });

    logger.info(`Call ${callId} ended successfully, duration: ${durationSeconds}s`);

    // Publish call ended event
    try {
      const event = createCallEndedEvent(callId, durationSeconds);
      await this.eventPublisher.publishEvent(event);
      logger.debug(`Published call ended event for call: ${callId}`);
    } catch (eventError) {
      // Log warning but don't fail the call ending
      logger.warn(`Failed to publish call ended event for call ${callId}:`, eventError);
    }

    return updatedCall;
  }

  /**
   * Get a call by its ID.
   * @param callId - The ID of the call to retrieve
   * @returns The call if found, null otherwise
   */
  async getCall(callId: string): Promise<Call | null> {
    return this.callRepository.findById(callId);
  }

  /**
   * Get all calls with optional status filter.
   * @param status - Optional status filter
   * @returns Array of calls
   */
  async getCalls(): Promise<Call[]> {
    return this.callRepository.findAll();
  }

}
