import { Request, Response, NextFunction } from 'express';
import { injectable } from 'tsyringe';
import { Call } from '@prisma/client';
import { z } from 'zod';
import { CallService } from './call.service';
import { getLogger } from '@/core/logger';
import { ApiError } from '@/utils/ApiError';
import { callParamsSchema } from './call.schema';

const logger = getLogger('CallsController');

// --- TYPES ---
type CallParams = z.infer<typeof callParamsSchema>;

// --- DTO / Response Mapper ---
// This function defines the public shape of a "Call" object in our API.
// It takes the internal Prisma model and returns a clean, safe object.
const toCallResponse = (call: Call) => {
  return {
    id: call.id,
    status: call.status,
    createdAt: call.createdAt.toISOString(),
    endedAt: call.endedAt? call.endedAt.toISOString() : null,
    mediaSessionId: call.mediaSessionId,
  };
};

@injectable()
export class CallController {
  constructor(private readonly callService: CallService) {}

  /**
   * Create a new call.
   */
  create = async (req: Request, res: Response, next: NextFunction) => {
    try {
      logger.info('Creating new call via API');
      const newCall = await this.callService.createNewCall();

      // Use the mapper to create a safe response object
      const response = toCallResponse(newCall);

      logger.info(`Call created successfully: ${newCall.id}`);
      res.status(201).json(response);
    } catch (error) {
      next(error); // Pass error to the global error handler
    }
  };

  /**
   * End a call.
   */
  end = async (req: Request<CallParams>, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      logger.info(`Ending call via API: ${id}`);

      await this.callService.endCall(id);

      logger.info(`Call ended successfully: ${id}`);
      res.status(204).send();
    } catch (error) {
      next(error);
    }
  };

  /**
   * Get a call by ID.
   */
  getById = async (req: Request<CallParams>, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      const call = await this.callService.getCall(id);

      if (!call) {
        logger.warn(`Call not found: ${id}`);
        throw new ApiError(404, 'Call not found');
      }

      const response = toCallResponse(call);
      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  };

  /**
   * Get all calls.
   */
  list = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const calls = await this.callService.getCalls();

      // Use the mapper on each item in the array
      const response = calls.map(toCallResponse);
      
      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  };
}