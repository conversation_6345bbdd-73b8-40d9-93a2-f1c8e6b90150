import { injectable, inject } from 'tsyringe';
import { Prisma, PrismaClient } from '@prisma/client';
import { INJECTION_TOKENS } from '@/utils/injection-tokens';


@injectable() 
export class CallRepository {
  constructor(
    @inject(INJECTION_TOKENS.PrismaClient) private readonly prisma: PrismaClient
  ) {}

  async create(data: Prisma.CallCreateInput) {
    return this.prisma.call.create({
      data,
    });
  }

  async findById(id: string) {
    return this.prisma.call.findUnique({
      where: { id },
    });
  }

  async update(id: string, data: Prisma.CallUpdateInput) {
    return this.prisma.call.update({
      where: { id },
      data,
    });
  }

  async findAll() {
    return this.prisma.call.findMany();
  }
}
