import { Router } from 'express';
import container from '@/container';
import { CallController } from './call.controller';
import { validate } from '@/middleware/validation.middleware';
import { callParamsSchema } from './call.schema';

const router = Router();
const callController = container.resolve(CallController);

/**
 * Create a new call.
 * POST /api/v1/calls
 */
router.post('/', callController.create);

/**
 * Get a call by ID.
 * GET /api/v1/calls/:id
 */
router.get('/:id', validate({ params: callParamsSchema }), callController.getById);

/**
 * End a call.
 * POST /api/v1/calls/:id/end
 */
router.post('/:id/end', validate({ params: callParamsSchema }), callController.end);

/**
 * List all calls.
 * GET /api/v1/calls
 */
router.get('/', callController.list);


export default router;