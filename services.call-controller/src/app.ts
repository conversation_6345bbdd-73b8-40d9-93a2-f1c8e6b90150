import express, { Application, Request, Response } from 'express';
import cors from 'cors';
import helmet from 'helmet';

import { config } from '@/config';
import { setupServiceLogging } from '@/core/logger';
import { setupMetrics, metricsMiddleware, metricsHandler } from '@/core/metrics';
import { errorHandler, notFoundHandler } from '@/middleware/error.middleware';

import callRoutes from '@/features/call/call.routes';


const app: Application = express();

// Application-wide services
setupServiceLogging(config.serviceName, config.debug? 'debug' : 'info');
setupMetrics();

// Apply security middleware
app.use(helmet({
  contentSecurityPolicy: false,
}));

// Cross-Origin Resource Sharing
app.use(cors({
  origin: '*', // Be more restrictive in production
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
}));

// Apply body parsing middleware with your specified limits
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Apply metrics middleware to track all requests
app.use(metricsMiddleware);

// --- API ROUTING ---

// A simple health check endpoint
app.get('/health', (req: Request, res: Response) => {
  res.status(200).json({ status: 'UP' });
});

// An endpoint to expose application metrics
app.get('/metrics', metricsHandler);

// Main API router for version 1
const apiV1Router = express.Router();
apiV1Router.use('/calls', callRoutes);

// Mount the versioned API router to the main application
app.use('/api/v1', apiV1Router);

// --- ERROR HANDLING ---

// Apply the 404 handler for any requests that don't match a route
app.use(notFoundHandler);

// Apply the global error handler. This must be the last middleware.
app.use(errorHandler);

export default app;
