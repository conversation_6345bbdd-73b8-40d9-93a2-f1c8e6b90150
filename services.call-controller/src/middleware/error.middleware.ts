import { Request, Response, NextFunction } from 'express';
import { ZodError } from 'zod';
import { getLogger } from '@/core/logger';
import { config } from '@/config';
import { ApiError } from '@/utils/ApiError';

const logger = getLogger('ErrorHandler');


/**
 * Middleware to handle requests for routes that are not found.
 * It creates a 404 error and passes it to the next error-handling middleware.
 */
export const notFoundHandler = (req: Request, res: Response, next: NextFunction) => {
  const error = new ApiError(404, `Not Found - ${req.originalUrl}`);
  next(error);
};

/**
 * Global error handling middleware.
 * This should be the last middleware in your Express application.
 * It catches and processes all errors passed to `next()`.
 */
export const errorHandler = (err: Error, req: Request, res: Response, next: NextFunction): void => {
  logger.error(err);

  // Handle Zod validation errors [2]
  if (err instanceof ZodError) {
    res.status(400).json({
      message: 'Validation failed',
      errors: err.issues.map((issue) => ({ path: issue.path, message: issue.message })),
    });
    return;
  }

  // Handle custom ApiError [1]
  if (err instanceof ApiError) {
    res.status(err.statusCode).json({
      message: err.message,
    });
    return;
  }

  // Handle other unexpected errors
  const statusCode = 500;
  const message = 'An internal server error occurred.';

  // In development, send detailed error info. In production, send a generic message.
  const response = config.nodeEnv === 'production'
   ? { message }
    : { message, stack: err.stack };

  res.status(statusCode).json(response);
};