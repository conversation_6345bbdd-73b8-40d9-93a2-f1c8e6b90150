import { Request, Response, NextFunction } from 'express';
import { ZodObject, ZodError, ZodType } from 'zod';
import { getLogger } from '@/core/logger';

const logger = getLogger('ValidationMiddleware');

/**
 * Validation schema configuration
 */
interface ValidationSchema {
  params?: ZodType<any>;
  query?: ZodType<any>;
  body?: ZodType<any>;
}

/**
 * A higher-order function that creates an Express middleware for validating
 * request data (params, query, body) against Zod schemas.
 *
 * @param schema - Either a ZodObject schema for the entire request, or an object with optional `params`, `query`, and `body` schemas.
 * @returns An Express middleware function.
 */
export const validate = (schema: ZodObject<any, any> | ValidationSchema) =>
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      // Check if it's a ValidationSchema object (has params, query, or body properties)
      if ('params' in schema || 'query' in schema || 'body' in schema) {
        const validationSchema = schema as ValidationSchema;

        // Validate each part separately if specified
        if (validationSchema.params) {
          await validationSchema.params.parseAsync(req.params);
        }
        if (validationSchema.query) {
          await validationSchema.query.parseAsync(req.query);
        }
        if (validationSchema.body) {
          await validationSchema.body.parseAsync(req.body);
        }
      } else {
        // Treat as a full request schema
        const fullSchema = schema as ZodObject<any, any>;
        await fullSchema.parseAsync({
          body: req.body,
          query: req.query,
          params: req.params,
        });
      }

      // If validation is successful, proceed to the next middleware or route handler.
      return next();
    } catch (error) {
      // If validation fails, Zod throws a ZodError.
      if (error instanceof ZodError) {
        logger.warn('Validation failed', { errors: error.issues });

        // We pass the ZodError to the global error handler.
        // The global error handler will then format a user-friendly response.
        return next(error);
      }

      // For any other unexpected errors, pass them along as well.
      logger.error('An unexpected error occurred during validation', { error });
      return next(error);
    }
};