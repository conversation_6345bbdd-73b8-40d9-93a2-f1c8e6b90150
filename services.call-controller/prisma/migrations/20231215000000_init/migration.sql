-- CreateEnum
CREATE TYPE "CallStatus" AS ENUM ('QUEUED', 'ACTIVE', 'ENDED');

-- CreateTable
CREATE TABLE "calls" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "status" "CallStatus" NOT NULL DEFAULT 'QUEUED',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "ended_at" TIMESTAMP(3),
    "media_session_id" TEXT,
    "translation_fork_id" TEXT,

    CONSTRAINT "calls_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "calls_media_session_id_key" ON "calls"("media_session_id");
