# Cortexa Call Controller Service (TypeScript)

This service manages the state of all calls and coordinates the various services involved in a call. It exposes a REST API for call lifecycle management and an internal API for service orchestration.

## Features

- **Call Management**: Create, track, and end voice calls
- **Service Orchestration**: Coordinates with voice-router service for media sessions
- **Event Publishing**: Publishes call lifecycle events to Kafka
- **Database Integration**: PostgreSQL with Prisma ORM
- **Metrics & Monitoring**: Prometheus metrics and structured logging

## Development

### Prerequisites
- Node.js 18+
- PostgreSQL
- Kafka

### Setup
```bash
# Install dependencies
npm install

# Copy environment file
cp .env.example .env

# Generate Prisma client
npm run db:generate

# Run database migrations
npm run migrate

# Start development server
npm run dev
```

### Testing
```bash
# Run tests
npm test

# Run tests in watch mode
npm run test:watch

# Generate coverage report
npm run test:coverage
```

### Building
```bash
# Build for production
npm run build

# Start production server
npm start
```
