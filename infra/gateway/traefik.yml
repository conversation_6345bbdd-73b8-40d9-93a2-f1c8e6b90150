http:
  middlewares:
    jwt-auth-role-authenticated:
      plugin:
        jwt:
          # This is the endpoint that will be used to validate the JWT token.
          issuers:
            - http://127.0.0.1:54321/auth/v1/

          # Allow token from query parameter for WebSocket connections
          parameterName: token
          
          # This specifies the claims that are required to be present in the JWT token.
          require:
            aud: authenticated

    # Middleware specifically for services requiring Administrator access.
    # This enforces the core RBAC policy.
    jwt-auth-role-administrator:
      plugin:
        jwt:
          # The issuer must still be Supabase.
          issuers:
            - http://127.0.0.1:54321/auth/v1/
          
          # This is the crucial RBAC enforcement block. It defines a policy
          # with two conditions, both of which MUST be true:
          # 1. The 'aud' claim must be 'authenticated'.
          # 2. The custom 'app_role' claim must exist and its value must be 'administrator'.
          require:
            aud: authenticated
            app_role: administrator