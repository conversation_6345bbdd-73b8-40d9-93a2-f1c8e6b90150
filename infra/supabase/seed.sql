-- Seed the role_permissions table to define the access control matrix for Cortexa.
INSERT INTO public.role_permissions (role, permission)
VALUES
    ('administrator', 'users.create'),
    ('administrator', 'users.read'),
    ('administrator', 'users.update'),
    ('administrator', 'users.delete'),
    ('administrator', 'reports.generate'),
    ('manager', 'users.read'),
    ('manager', 'calls.monitor'),
    ('manager', 'reports.generate'),
    ('operator', 'calls.assign'),
    ('translator', 'calls.assign')
ON CONFLICT (role, permission) DO NOTHING; -- Ensures the script is re-runnable without errors.

-- Seed the initial Administrator User for development and testing environments.
-- 1. Create the user directly in the Supabase Auth schema.
INSERT INTO auth.users (instance_id, id, aud, role, email, encrypted_password, email_confirmed_at, raw_app_meta_data, raw_user_meta_data, created_at, updated_at)
VALUES
    ('00000000-0000-0000-0000-000000000000', '8a5e9a9a-4933-4d54-a859-336b911a3d24', 'authenticated', 'authenticated', '<EMAIL>', crypt('password123', gen_salt('bf')), NOW(), '{"provider":"email","providers":["email"]}', '{}', NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- 2. Assign the 'administrator' role to the newly created user.
INSERT INTO public.user_roles (user_id, role)
VALUES
    ('8a5e9a9a-4933-4d54-a859-336b911a3d24', 'administrator')
ON CONFLICT (user_id, role) DO NOTHING;